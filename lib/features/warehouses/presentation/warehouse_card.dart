import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:sphere/core/gen/assets.gen.dart';
import 'package:sphere/features/purchase_list/data/models/index.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/styles/fonts.dart';
import 'package:sphere/shared/widgets/svg/index.dart';

class WarehouseCard extends StatelessWidget {
  const WarehouseCard({
    super.key,
    this.warehouse,
    this.isLoading = false,
    this.onTap,
    this.onEdit,
    this.onDelete,
    this.onSecondaryTapDown,
  });

  final Warehouse? warehouse;
  final bool isLoading;
  final VoidCallback? onTap;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final void Function(TapDownDetails)? onSecondaryTapDown;

  @override
  Widget build(BuildContext context) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;
    final backgroundColor =
        isDarkTheme ? AppColors.darkSurface : AppColors.lightSurface;
    final strokeColor =
        isDarkTheme ? AppColors.darkStroke : AppColors.lightStroke;

    if (isLoading) {
      return _buildLoadingCard(backgroundColor, strokeColor);
    }

    return GestureDetector(
      onTap: onTap,
      onSecondaryTapDown: onSecondaryTapDown ?? _defaultSecondaryTapDown,
      child: Container(
        decoration: BoxDecoration(
          // color: backgroundColor,
          borderRadius: BorderRadius.circular(12.0),
          border: Border.all(color: strokeColor),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(isDarkTheme),
            _buildContent(isDarkTheme),
            // if (warehouse?.address != null || warehouse?.description != null)
            //   _buildFooter(isDarkTheme),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingCard(Color backgroundColor, Color strokeColor) {
    return Container(
      height: 120,
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(12.0),
        border: Border.all(color: strokeColor),
      ),
      child: const Center(
        child: CircularProgressIndicator(),
      ),
    );
  }

  Widget _buildHeader(bool isDarkTheme) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: _getTypeColor().withOpacity(0.05),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(12.0),
          topRight: Radius.circular(12.0),
        ),
      ),
      child: Row(
        children: [
          // Container(
          //   padding: const EdgeInsets.all(8.0),
          //   decoration: BoxDecoration(
          //     color: _getTypeColor().withOpacity(0.2),
          //     borderRadius: BorderRadius.circular(8.0),
          //   ),
          //   child: SVG(
          //     _getTypeIcon(),
          //     width: 20.0,
          //     color: _getTypeColor(),
          //   ),
          // ),
          // const SizedBox(width: 12.0),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  warehouse?.name ?? 'Склад',
                  style: Fonts.titleSmall.merge(
                    TextStyle(
                      color: isDarkTheme
                          ? AppColors.darkPrimary
                          : AppColors.lightPrimary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4.0),
                Container(
                  padding: const EdgeInsets.symmetric(
                      horizontal: 8.0, vertical: 2.0),
                  decoration: BoxDecoration(
                    color: _getTypeColor(),
                    borderRadius: BorderRadius.circular(12.0),
                  ),
                  child: Text(
                    _getTypeLabel(),
                    style: Fonts.labelSmall.merge(
                      const TextStyle(
                        color: Colors.white,
                        fontSize: 10.0,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent(bool isDarkTheme) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (warehouse?.address != null) ...[
            Row(
              children: [
                Icon(
                  CupertinoIcons.location,
                  color: isDarkTheme
                      ? AppColors.darkDescription
                      : AppColors.lightDescription,
                ),
                const SizedBox(width: 8.0),
                Expanded(
                  child: Text(
                    warehouse!.address!,
                    style: Fonts.bodySmall.merge(
                      TextStyle(
                        color: isDarkTheme
                            ? AppColors.darkDescription
                            : AppColors.lightDescription,
                      ),
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8.0),
          ],
          if (warehouse?.description != null) ...[
            Text(
              warehouse!.description!,
              style: Fonts.bodySmall.merge(
                TextStyle(
                  color: isDarkTheme
                      ? AppColors.darkDescription
                      : AppColors.lightDescription,
                ),
              ),
              maxLines: 3,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildFooter(bool isDarkTheme) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: isDarkTheme
            ? AppColors.darkBackground.withOpacity(0.5)
            : AppColors.lightBackground.withOpacity(0.5),
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(12.0),
          bottomRight: Radius.circular(12.0),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Text(
              'Нажмите для просмотра содержимого',
              style: Fonts.labelSmall.merge(
                TextStyle(
                  color: isDarkTheme
                      ? AppColors.darkDescription
                      : AppColors.lightDescription,
                ),
              ),
            ),
          ),
          SVG(
            Assets.icons.arrowForwardIos,
            width: 16.0,
            color: isDarkTheme
                ? AppColors.darkDescription
                : AppColors.lightDescription,
          ),
        ],
      ),
    );
  }

  void _defaultSecondaryTapDown(TapDownDetails details) {
    if (warehouse == null) return;

    // This would typically show a context menu
    // For now, we'll just call the edit function if available
    onEdit?.call();
  }

  Color _getTypeColor() {
    switch (warehouse?.type) {
      case WarehouseType.general:
        return AppColors.lightSecondary;
      case WarehouseType.cold:
        return AppColors.lightPurple;
      case WarehouseType.hazardous:
        return AppColors.lightWarning;
      default:
        return AppColors.medium;
    }
  }

  String _getTypeIcon() {
    switch (warehouse?.type) {
      case WarehouseType.general:
        return Assets.icons.warehouse;
      case WarehouseType.cold:
        return Assets.icons.pageInfo;
      case WarehouseType.hazardous:
        return Assets.icons.delete;
      default:
        return Assets.icons.warehouse;
    }
  }

  String _getTypeLabel() {
    switch (warehouse?.type) {
      case WarehouseType.general:
        return 'Общий';
      case WarehouseType.cold:
        return 'Холодильный';
      case WarehouseType.hazardous:
        return 'Опасные материалы';
      default:
        return 'Неизвестно';
    }
  }
}
