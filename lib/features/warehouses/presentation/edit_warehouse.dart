import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import 'package:sphere/features/purchase_list/data/models/index.dart';
import 'package:sphere/features/purchase_list/data/repositories/new.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/styles/fonts.dart';
import 'package:sphere/shared/widgets/interactive/elevated_button.dart';

class EditWarehousePopup extends StatefulWidget {
  const EditWarehousePopup({
    super.key,
    required this.warehouse,
    this.onSuccess,
  });

  final Warehouse warehouse;
  final VoidCallback? onSuccess;

  @override
  State<EditWarehousePopup> createState() => _EditWarehousePopupState();
}

class _EditWarehousePopupState extends State<EditWarehousePopup> {
  late final TextEditingController _nameController;
  late final TextEditingController _addressController;
  late final TextEditingController _descriptionController;

  bool _isLoading = false;
  String? _nameError;
  String? _addressError;

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(text: widget.warehouse.name);
    _addressController =
        TextEditingController(text: widget.warehouse.address ?? '');
    _descriptionController =
        TextEditingController(text: widget.warehouse.description ?? '');
  }

  @override
  void dispose() {
    _nameController.dispose();
    _addressController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  bool _validateForm() {
    setState(() {
      _nameError = null;
      _addressError = null;
    });

    bool isValid = true;

    if (_nameController.text.trim().isEmpty) {
      setState(() {
        _nameError = 'Название склада обязательно';
      });
      isValid = false;
    } else if (_nameController.text.trim().length < 2) {
      setState(() {
        _nameError = 'Название должно содержать минимум 2 символа';
      });
      isValid = false;
    }

    if (_addressController.text.trim().isEmpty) {
      setState(() {
        _addressError = 'Адрес склада обязателен';
      });
      isValid = false;
    }

    return isValid;
  }

  bool _hasChanges() {
    return _nameController.text.trim() != widget.warehouse.name ||
        _addressController.text.trim() != (widget.warehouse.address ?? '') ||
        _descriptionController.text.trim() !=
            (widget.warehouse.description ?? '');
  }

  Future<void> _updateWarehouse() async {
    if (!_validateForm()) return;

    if (!_hasChanges()) {
      Navigator.of(context).pop();
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final warehouseInput = WarehouseUpdateInput(
        warehouseId: widget.warehouse.id!,
        name: _nameController.text.trim() != widget.warehouse.name
            ? _nameController.text.trim()
            : null,
        address:
            _addressController.text.trim() != (widget.warehouse.address ?? '')
                ? _addressController.text.trim()
                : null,
        description: _descriptionController.text.trim() !=
                (widget.warehouse.description ?? '')
            ? (_descriptionController.text.trim().isEmpty
                ? null
                : _descriptionController.text.trim())
            : null,
      );

      final result =
          await PurchaseListRepositoryV2.updateWarehouse(warehouseInput);

      if (result.data != null) {
        final logger = Logger();
        logger.d('Warehouse updated successfully: ${result.data}');

        widget.onSuccess?.call();

        if (mounted) {
          Navigator.of(context).pop();
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Склад успешно обновлен'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        throw Exception(result.data?.message ?? 'Ошибка при обновлении склада');
      }
    } catch (e) {
      final logger = Logger();
      logger.e('Error updating warehouse: $e');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Ошибка при обновлении склада: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

    return Column(
      children: [
        Expanded(
          child: ListView(
            padding: const EdgeInsets.all(20.0),
            children: [
              Text(
                'Редактирование склада',
                style: Fonts.titleMedium.merge(
                  const TextStyle(height: 1.6),
                ),
              ),
              const SizedBox(height: 8.0),
              Text(
                'ID: ${widget.warehouse.id}',
                style: Fonts.bodySmall.merge(
                  TextStyle(
                    color: isDarkTheme
                        ? AppColors.darkDescription
                        : AppColors.lightDescription,
                  ),
                ),
              ),
              const SizedBox(height: 20.0),

              // Name field
              Text(
                'Название склада *',
                style: Fonts.bodyMedium.merge(
                  const TextStyle(height: 1.5),
                ),
              ),
              const SizedBox(height: 8.0),
              TextField(
                controller: _nameController,
                // hintText: 'Введите название склада',
                // errorText: _nameError,
                onChanged: (value) {
                  if (_nameError != null) {
                    setState(() {
                      _nameError = null;
                    });
                  }
                },
              ),
              const SizedBox(height: 16.0),

              // Type field (read-only)
              Text(
                'Тип склада',
                style: Fonts.bodyMedium.merge(
                  const TextStyle(height: 1.5),
                ),
              ),
              const SizedBox(height: 8.0),
              Container(
                padding: const EdgeInsets.all(12.0),
                decoration: BoxDecoration(
                  color: isDarkTheme
                      ? AppColors.darkSurface.withOpacity(0.3)
                      : AppColors.lightSurface.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(8.0),
                  border: Border.all(
                    color: isDarkTheme
                        ? AppColors.darkStroke
                        : AppColors.lightStroke,
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.lock_outline,
                      size: 16.0,
                      color: isDarkTheme
                          ? AppColors.darkDescription
                          : AppColors.lightDescription,
                    ),
                    const SizedBox(width: 8.0),
                    Text(
                      _getTypeLabel(widget.warehouse.type),
                      style: Fonts.bodyMedium.merge(
                        TextStyle(
                          color: isDarkTheme
                              ? AppColors.darkDescription
                              : AppColors.lightDescription,
                        ),
                      ),
                    ),
                    const Spacer(),
                    Text(
                      'Нельзя изменить',
                      style: Fonts.labelSmall.merge(
                        TextStyle(
                          color: isDarkTheme
                              ? AppColors.darkDescription
                              : AppColors.lightDescription,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16.0),

              // Address field
              Text(
                'Адрес склада *',
                style: Fonts.bodyMedium.merge(
                  const TextStyle(height: 1.5),
                ),
              ),
              const SizedBox(height: 8.0),
              TextField(
                controller: _addressController,
                // hintText: 'Введите адрес склада',
                // errorText: _addressError,
                maxLines: 2,
                onChanged: (value) {
                  if (_addressError != null) {
                    setState(() {
                      _addressError = null;
                    });
                  }
                },
              ),
              const SizedBox(height: 16.0),

              // Description field
              Text(
                'Описание (необязательно)',
                style: Fonts.bodyMedium.merge(
                  const TextStyle(height: 1.5),
                ),
              ),
              const SizedBox(height: 8.0),
              TextField(
                controller: _descriptionController,
                // hintText: 'Введите описание склада',
                maxLines: 3,
              ),
              const SizedBox(height: 16.0),

              // Changes indicator
              if (_hasChanges())
                Container(
                  padding: const EdgeInsets.all(12.0),
                  decoration: BoxDecoration(
                    color: Colors.orange.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8.0),
                    border: Border.all(color: Colors.orange.withOpacity(0.3)),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.edit_outlined,
                        size: 16.0,
                        color: Colors.orange,
                      ),
                      const SizedBox(width: 8.0),
                      Text(
                        'Есть несохраненные изменения',
                        style: Fonts.labelMedium.merge(
                          const TextStyle(color: Colors.orange),
                        ),
                      ),
                    ],
                  ),
                ),
            ],
          ),
        ),

        // Action buttons
        Container(
          padding: const EdgeInsets.all(20.0),
          decoration: BoxDecoration(
            color: isDarkTheme
                ? AppColors.darkBackground
                : AppColors.lightBackground,
            border: BorderDirectional(
              top: BorderSide(
                color:
                    isDarkTheme ? AppColors.darkStroke : AppColors.lightStroke,
              ),
            ),
          ),
          child: Row(
            children: [
              Expanded(
                child: CustomElevatedButton(
                  onPressed: () {
                    if (!_isLoading) {
                      Navigator.of(context).pop();
                    }
                  },
                  text: 'Отмена',
                ),
              ),
              const SizedBox(width: 12.0),
              Expanded(
                child: CustomElevatedButton(
                  type: CustomElevatedButtonTypes.accent,
                  onPressed: () {
                    if (!_isLoading) {
                      _updateWarehouse();
                    }
                  },
                  text: _isLoading ? 'Сохранение...' : 'Сохранить изменения',
                  disabled: _isLoading,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  String _getTypeLabel(WarehouseType type) {
    switch (type) {
      case WarehouseType.general:
        return 'Общий склад';
      case WarehouseType.contractor:
        return 'Склад контрагента';
      case WarehouseType.virtualProject:
        return 'Виртуальный склад проекта';
    }
  }
}
