# Интеграция поиска складов в создание/редактирование контрактов

## Обзор

Добавлена улучшенная функциональность поиска и выбора складов в компоненте `ProductDetailsSectionV2`, который используется при создании и редактировании контрактов.

## Новые возможности

### 1. Поиск складов в реальном времени
- Поле поиска в выпадающем списке складов
- Поиск по названию, адресу и описанию склада
- Фильтрация результатов в реальном времени

### 2. Улучшенное отображение складов
- Иконки для разных типов складов:
  - 🏢 Обычный склад (синий)
  - ❄️ Холодильный склад (голубой)
  - ⚠️ Склад опасных веществ (оранжевый)
- Отображение типа склада, адреса и описания
- Цветовая индикация типов складов

### 3. Детальная информация о складах
- Название склада
- Тип склада с цветовой индикацией
- Адрес (если указан)
- Описание (если указано)

## Использование

### В создании контракта
```dart
showBaseDialog(
  context,
  maxWidth: 720,
  builder: (context) => CreateContractBody(
    projectId: projectId,
    selectedProductIds: selectedProductIds,
    onContractCreated: () {
      // Обновить данные
    },
  ),
);
```

### В редактировании контракта
```dart
showBaseDialog(
  context,
  maxWidth: 720,
  builder: (context) => CreateContractBody(
    projectId: projectId,
    selectedProductIds: contract.productIds ?? [],
    existingContract: contract,
    onContractCreated: () {
      // Обновить данные
    },
  ),
);
```

## Структура данных

### Warehouse
```dart
class Warehouse {
  String? id;
  String name;
  WarehouseType type;
  String? address;
  String? description;
}
```

### WarehouseType
```dart
enum WarehouseType {
  general,    // Обычный склад
  cold,       // Холодильный склад
  hazardous,  // Склад опасных веществ
}
```

### Distribution
```dart
class Distribution {
  String? warehouseId;  // ID выбранного склада
  double? quantity;     // Количество для распределения
  String? deliveryDate; // Дата поставки
  String? notes;        // Примечания
}
```

## API интеграция

### Получение списка складов
```dart
final response = await PurchaseListRepositoryV2.searchWarehouses();
```

Ответ API может быть в двух форматах:
1. `{ "data": [warehouse1, warehouse2, ...] }`
2. `[warehouse1, warehouse2, ...]`

## Особенности реализации

### 1. Управление состоянием поиска
- Отдельные контроллеры поиска для каждого распределения
- Фильтрация складов по запросу пользователя
- Очистка поля поиска после выбора склада

### 2. Null safety
- Корректная обработка случаев, когда склад не выбран
- Проверка существования склада в списке
- Безопасное отображение опциональных полей

### 3. Производительность
- Ленивая инициализация контроллеров поиска
- Правильная очистка ресурсов в dispose()
- Эффективная фильтрация списка складов

## Интеграция с контрактами

Выбранные склады автоматически сохраняются в структуре `ProductDetail` и передаются в API при создании/обновлении контракта:

```dart
final contractInput = ProvisionCreateContractInput(
  // ... другие поля
  productDetails: _productDetails, // Содержит информацию о складах
);
```

## Визуальные улучшения

1. **Цветовая схема**: Каждый тип склада имеет свой цвет
2. **Иконки**: Интуитивные иконки для типов складов
3. **Компактное отображение**: Вся важная информация в удобном формате
4. **Поиск**: Удобное поле поиска с иконкой
5. **Детали**: Полная информация о складе в выпадающем списке
