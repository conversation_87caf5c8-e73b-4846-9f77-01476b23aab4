// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'index.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

GetRequirementsOutput _$GetRequirementsOutputFromJson(
    Map<String, dynamic> json) {
  return _GetRequirementsOutput.fromJson(json);
}

/// @nodoc
mixin _$GetRequirementsOutput {
  List<ProvisionProductModel>? get items => throw _privateConstructorUsedError;
  int? get totalItems => throw _privateConstructorUsedError;

  /// Serializes this GetRequirementsOutput to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of GetRequirementsOutput
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $GetRequirementsOutputCopyWith<GetRequirementsOutput> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $GetRequirementsOutputCopyWith<$Res> {
  factory $GetRequirementsOutputCopyWith(GetRequirementsOutput value,
          $Res Function(GetRequirementsOutput) then) =
      _$GetRequirementsOutputCopyWithImpl<$Res, GetRequirementsOutput>;
  @useResult
  $Res call({List<ProvisionProductModel>? items, int? totalItems});
}

/// @nodoc
class _$GetRequirementsOutputCopyWithImpl<$Res,
        $Val extends GetRequirementsOutput>
    implements $GetRequirementsOutputCopyWith<$Res> {
  _$GetRequirementsOutputCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of GetRequirementsOutput
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? items = freezed,
    Object? totalItems = freezed,
  }) {
    return _then(_value.copyWith(
      items: freezed == items
          ? _value.items
          : items // ignore: cast_nullable_to_non_nullable
              as List<ProvisionProductModel>?,
      totalItems: freezed == totalItems
          ? _value.totalItems
          : totalItems // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$GetRequirementsOutputImplCopyWith<$Res>
    implements $GetRequirementsOutputCopyWith<$Res> {
  factory _$$GetRequirementsOutputImplCopyWith(
          _$GetRequirementsOutputImpl value,
          $Res Function(_$GetRequirementsOutputImpl) then) =
      __$$GetRequirementsOutputImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<ProvisionProductModel>? items, int? totalItems});
}

/// @nodoc
class __$$GetRequirementsOutputImplCopyWithImpl<$Res>
    extends _$GetRequirementsOutputCopyWithImpl<$Res,
        _$GetRequirementsOutputImpl>
    implements _$$GetRequirementsOutputImplCopyWith<$Res> {
  __$$GetRequirementsOutputImplCopyWithImpl(_$GetRequirementsOutputImpl _value,
      $Res Function(_$GetRequirementsOutputImpl) _then)
      : super(_value, _then);

  /// Create a copy of GetRequirementsOutput
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? items = freezed,
    Object? totalItems = freezed,
  }) {
    return _then(_$GetRequirementsOutputImpl(
      items: freezed == items
          ? _value._items
          : items // ignore: cast_nullable_to_non_nullable
              as List<ProvisionProductModel>?,
      totalItems: freezed == totalItems
          ? _value.totalItems
          : totalItems // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$GetRequirementsOutputImpl implements _GetRequirementsOutput {
  _$GetRequirementsOutputImpl(
      {final List<ProvisionProductModel>? items, this.totalItems})
      : _items = items;

  factory _$GetRequirementsOutputImpl.fromJson(Map<String, dynamic> json) =>
      _$$GetRequirementsOutputImplFromJson(json);

  final List<ProvisionProductModel>? _items;
  @override
  List<ProvisionProductModel>? get items {
    final value = _items;
    if (value == null) return null;
    if (_items is EqualUnmodifiableListView) return _items;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final int? totalItems;

  @override
  String toString() {
    return 'GetRequirementsOutput(items: $items, totalItems: $totalItems)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GetRequirementsOutputImpl &&
            const DeepCollectionEquality().equals(other._items, _items) &&
            (identical(other.totalItems, totalItems) ||
                other.totalItems == totalItems));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_items), totalItems);

  /// Create a copy of GetRequirementsOutput
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$GetRequirementsOutputImplCopyWith<_$GetRequirementsOutputImpl>
      get copyWith => __$$GetRequirementsOutputImplCopyWithImpl<
          _$GetRequirementsOutputImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$GetRequirementsOutputImplToJson(
      this,
    );
  }
}

abstract class _GetRequirementsOutput implements GetRequirementsOutput {
  factory _GetRequirementsOutput(
      {final List<ProvisionProductModel>? items,
      final int? totalItems}) = _$GetRequirementsOutputImpl;

  factory _GetRequirementsOutput.fromJson(Map<String, dynamic> json) =
      _$GetRequirementsOutputImpl.fromJson;

  @override
  List<ProvisionProductModel>? get items;
  @override
  int? get totalItems;

  /// Create a copy of GetRequirementsOutput
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$GetRequirementsOutputImplCopyWith<_$GetRequirementsOutputImpl>
      get copyWith => throw _privateConstructorUsedError;
}

ProvisionsCreateModel _$ProvisionsCreateModelFromJson(
    Map<String, dynamic> json) {
  return _ProvisionsCreateModel.fromJson(json);
}

/// @nodoc
mixin _$ProvisionsCreateModel {
  String? get provisionName => throw _privateConstructorUsedError;
  ProvisionsFilter? get filterType => throw _privateConstructorUsedError;
  List<ProvisionsCreateItemModel>? get items =>
      throw _privateConstructorUsedError;
  List<DeliveryGroupModel>? get deliveryGroups =>
      throw _privateConstructorUsedError;

  /// Serializes this ProvisionsCreateModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProvisionsCreateModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProvisionsCreateModelCopyWith<ProvisionsCreateModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProvisionsCreateModelCopyWith<$Res> {
  factory $ProvisionsCreateModelCopyWith(ProvisionsCreateModel value,
          $Res Function(ProvisionsCreateModel) then) =
      _$ProvisionsCreateModelCopyWithImpl<$Res, ProvisionsCreateModel>;
  @useResult
  $Res call(
      {String? provisionName,
      ProvisionsFilter? filterType,
      List<ProvisionsCreateItemModel>? items,
      List<DeliveryGroupModel>? deliveryGroups});
}

/// @nodoc
class _$ProvisionsCreateModelCopyWithImpl<$Res,
        $Val extends ProvisionsCreateModel>
    implements $ProvisionsCreateModelCopyWith<$Res> {
  _$ProvisionsCreateModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProvisionsCreateModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? provisionName = freezed,
    Object? filterType = freezed,
    Object? items = freezed,
    Object? deliveryGroups = freezed,
  }) {
    return _then(_value.copyWith(
      provisionName: freezed == provisionName
          ? _value.provisionName
          : provisionName // ignore: cast_nullable_to_non_nullable
              as String?,
      filterType: freezed == filterType
          ? _value.filterType
          : filterType // ignore: cast_nullable_to_non_nullable
              as ProvisionsFilter?,
      items: freezed == items
          ? _value.items
          : items // ignore: cast_nullable_to_non_nullable
              as List<ProvisionsCreateItemModel>?,
      deliveryGroups: freezed == deliveryGroups
          ? _value.deliveryGroups
          : deliveryGroups // ignore: cast_nullable_to_non_nullable
              as List<DeliveryGroupModel>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ProvisionsCreateModelImplCopyWith<$Res>
    implements $ProvisionsCreateModelCopyWith<$Res> {
  factory _$$ProvisionsCreateModelImplCopyWith(
          _$ProvisionsCreateModelImpl value,
          $Res Function(_$ProvisionsCreateModelImpl) then) =
      __$$ProvisionsCreateModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? provisionName,
      ProvisionsFilter? filterType,
      List<ProvisionsCreateItemModel>? items,
      List<DeliveryGroupModel>? deliveryGroups});
}

/// @nodoc
class __$$ProvisionsCreateModelImplCopyWithImpl<$Res>
    extends _$ProvisionsCreateModelCopyWithImpl<$Res,
        _$ProvisionsCreateModelImpl>
    implements _$$ProvisionsCreateModelImplCopyWith<$Res> {
  __$$ProvisionsCreateModelImplCopyWithImpl(_$ProvisionsCreateModelImpl _value,
      $Res Function(_$ProvisionsCreateModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProvisionsCreateModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? provisionName = freezed,
    Object? filterType = freezed,
    Object? items = freezed,
    Object? deliveryGroups = freezed,
  }) {
    return _then(_$ProvisionsCreateModelImpl(
      provisionName: freezed == provisionName
          ? _value.provisionName
          : provisionName // ignore: cast_nullable_to_non_nullable
              as String?,
      filterType: freezed == filterType
          ? _value.filterType
          : filterType // ignore: cast_nullable_to_non_nullable
              as ProvisionsFilter?,
      items: freezed == items
          ? _value._items
          : items // ignore: cast_nullable_to_non_nullable
              as List<ProvisionsCreateItemModel>?,
      deliveryGroups: freezed == deliveryGroups
          ? _value._deliveryGroups
          : deliveryGroups // ignore: cast_nullable_to_non_nullable
              as List<DeliveryGroupModel>?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$ProvisionsCreateModelImpl implements _ProvisionsCreateModel {
  const _$ProvisionsCreateModelImpl(
      {this.provisionName,
      this.filterType,
      final List<ProvisionsCreateItemModel>? items,
      final List<DeliveryGroupModel>? deliveryGroups})
      : _items = items,
        _deliveryGroups = deliveryGroups;

  factory _$ProvisionsCreateModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProvisionsCreateModelImplFromJson(json);

  @override
  final String? provisionName;
  @override
  final ProvisionsFilter? filterType;
  final List<ProvisionsCreateItemModel>? _items;
  @override
  List<ProvisionsCreateItemModel>? get items {
    final value = _items;
    if (value == null) return null;
    if (_items is EqualUnmodifiableListView) return _items;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<DeliveryGroupModel>? _deliveryGroups;
  @override
  List<DeliveryGroupModel>? get deliveryGroups {
    final value = _deliveryGroups;
    if (value == null) return null;
    if (_deliveryGroups is EqualUnmodifiableListView) return _deliveryGroups;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'ProvisionsCreateModel(provisionName: $provisionName, filterType: $filterType, items: $items, deliveryGroups: $deliveryGroups)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProvisionsCreateModelImpl &&
            (identical(other.provisionName, provisionName) ||
                other.provisionName == provisionName) &&
            (identical(other.filterType, filterType) ||
                other.filterType == filterType) &&
            const DeepCollectionEquality().equals(other._items, _items) &&
            const DeepCollectionEquality()
                .equals(other._deliveryGroups, _deliveryGroups));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      provisionName,
      filterType,
      const DeepCollectionEquality().hash(_items),
      const DeepCollectionEquality().hash(_deliveryGroups));

  /// Create a copy of ProvisionsCreateModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProvisionsCreateModelImplCopyWith<_$ProvisionsCreateModelImpl>
      get copyWith => __$$ProvisionsCreateModelImplCopyWithImpl<
          _$ProvisionsCreateModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProvisionsCreateModelImplToJson(
      this,
    );
  }
}

abstract class _ProvisionsCreateModel implements ProvisionsCreateModel {
  const factory _ProvisionsCreateModel(
          {final String? provisionName,
          final ProvisionsFilter? filterType,
          final List<ProvisionsCreateItemModel>? items,
          final List<DeliveryGroupModel>? deliveryGroups}) =
      _$ProvisionsCreateModelImpl;

  factory _ProvisionsCreateModel.fromJson(Map<String, dynamic> json) =
      _$ProvisionsCreateModelImpl.fromJson;

  @override
  String? get provisionName;
  @override
  ProvisionsFilter? get filterType;
  @override
  List<ProvisionsCreateItemModel>? get items;
  @override
  List<DeliveryGroupModel>? get deliveryGroups;

  /// Create a copy of ProvisionsCreateModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProvisionsCreateModelImplCopyWith<_$ProvisionsCreateModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}

ProvisionsCreateItemModel _$ProvisionsCreateItemModelFromJson(
    Map<String, dynamic> json) {
  return _ProvisionsCreateItemModel.fromJson(json);
}

/// @nodoc
mixin _$ProvisionsCreateItemModel {
  String? get productId => throw _privateConstructorUsedError;
  ParametersFeatureType? get featureType => throw _privateConstructorUsedError;
  double? get quantity => throw _privateConstructorUsedError;

  /// Serializes this ProvisionsCreateItemModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProvisionsCreateItemModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProvisionsCreateItemModelCopyWith<ProvisionsCreateItemModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProvisionsCreateItemModelCopyWith<$Res> {
  factory $ProvisionsCreateItemModelCopyWith(ProvisionsCreateItemModel value,
          $Res Function(ProvisionsCreateItemModel) then) =
      _$ProvisionsCreateItemModelCopyWithImpl<$Res, ProvisionsCreateItemModel>;
  @useResult
  $Res call(
      {String? productId,
      ParametersFeatureType? featureType,
      double? quantity});
}

/// @nodoc
class _$ProvisionsCreateItemModelCopyWithImpl<$Res,
        $Val extends ProvisionsCreateItemModel>
    implements $ProvisionsCreateItemModelCopyWith<$Res> {
  _$ProvisionsCreateItemModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProvisionsCreateItemModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? productId = freezed,
    Object? featureType = freezed,
    Object? quantity = freezed,
  }) {
    return _then(_value.copyWith(
      productId: freezed == productId
          ? _value.productId
          : productId // ignore: cast_nullable_to_non_nullable
              as String?,
      featureType: freezed == featureType
          ? _value.featureType
          : featureType // ignore: cast_nullable_to_non_nullable
              as ParametersFeatureType?,
      quantity: freezed == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as double?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ProvisionsCreateItemModelImplCopyWith<$Res>
    implements $ProvisionsCreateItemModelCopyWith<$Res> {
  factory _$$ProvisionsCreateItemModelImplCopyWith(
          _$ProvisionsCreateItemModelImpl value,
          $Res Function(_$ProvisionsCreateItemModelImpl) then) =
      __$$ProvisionsCreateItemModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? productId,
      ParametersFeatureType? featureType,
      double? quantity});
}

/// @nodoc
class __$$ProvisionsCreateItemModelImplCopyWithImpl<$Res>
    extends _$ProvisionsCreateItemModelCopyWithImpl<$Res,
        _$ProvisionsCreateItemModelImpl>
    implements _$$ProvisionsCreateItemModelImplCopyWith<$Res> {
  __$$ProvisionsCreateItemModelImplCopyWithImpl(
      _$ProvisionsCreateItemModelImpl _value,
      $Res Function(_$ProvisionsCreateItemModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProvisionsCreateItemModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? productId = freezed,
    Object? featureType = freezed,
    Object? quantity = freezed,
  }) {
    return _then(_$ProvisionsCreateItemModelImpl(
      productId: freezed == productId
          ? _value.productId
          : productId // ignore: cast_nullable_to_non_nullable
              as String?,
      featureType: freezed == featureType
          ? _value.featureType
          : featureType // ignore: cast_nullable_to_non_nullable
              as ParametersFeatureType?,
      quantity: freezed == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as double?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$ProvisionsCreateItemModelImpl implements _ProvisionsCreateItemModel {
  const _$ProvisionsCreateItemModelImpl(
      {this.productId, this.featureType, this.quantity});

  factory _$ProvisionsCreateItemModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProvisionsCreateItemModelImplFromJson(json);

  @override
  final String? productId;
  @override
  final ParametersFeatureType? featureType;
  @override
  final double? quantity;

  @override
  String toString() {
    return 'ProvisionsCreateItemModel(productId: $productId, featureType: $featureType, quantity: $quantity)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProvisionsCreateItemModelImpl &&
            (identical(other.productId, productId) ||
                other.productId == productId) &&
            (identical(other.featureType, featureType) ||
                other.featureType == featureType) &&
            (identical(other.quantity, quantity) ||
                other.quantity == quantity));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, productId, featureType, quantity);

  /// Create a copy of ProvisionsCreateItemModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProvisionsCreateItemModelImplCopyWith<_$ProvisionsCreateItemModelImpl>
      get copyWith => __$$ProvisionsCreateItemModelImplCopyWithImpl<
          _$ProvisionsCreateItemModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProvisionsCreateItemModelImplToJson(
      this,
    );
  }
}

abstract class _ProvisionsCreateItemModel implements ProvisionsCreateItemModel {
  const factory _ProvisionsCreateItemModel(
      {final String? productId,
      final ParametersFeatureType? featureType,
      final double? quantity}) = _$ProvisionsCreateItemModelImpl;

  factory _ProvisionsCreateItemModel.fromJson(Map<String, dynamic> json) =
      _$ProvisionsCreateItemModelImpl.fromJson;

  @override
  String? get productId;
  @override
  ParametersFeatureType? get featureType;
  @override
  double? get quantity;

  /// Create a copy of ProvisionsCreateItemModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProvisionsCreateItemModelImplCopyWith<_$ProvisionsCreateItemModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}

ProvisionsGetMaterials _$ProvisionsGetMaterialsFromJson(
    Map<String, dynamic> json) {
  return _ProvisionsGetMaterials.fromJson(json);
}

/// @nodoc
mixin _$ProvisionsGetMaterials {
  ProvisionsFilter? get filter => throw _privateConstructorUsedError;

  /// Serializes this ProvisionsGetMaterials to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProvisionsGetMaterials
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProvisionsGetMaterialsCopyWith<ProvisionsGetMaterials> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProvisionsGetMaterialsCopyWith<$Res> {
  factory $ProvisionsGetMaterialsCopyWith(ProvisionsGetMaterials value,
          $Res Function(ProvisionsGetMaterials) then) =
      _$ProvisionsGetMaterialsCopyWithImpl<$Res, ProvisionsGetMaterials>;
  @useResult
  $Res call({ProvisionsFilter? filter});
}

/// @nodoc
class _$ProvisionsGetMaterialsCopyWithImpl<$Res,
        $Val extends ProvisionsGetMaterials>
    implements $ProvisionsGetMaterialsCopyWith<$Res> {
  _$ProvisionsGetMaterialsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProvisionsGetMaterials
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? filter = freezed,
  }) {
    return _then(_value.copyWith(
      filter: freezed == filter
          ? _value.filter
          : filter // ignore: cast_nullable_to_non_nullable
              as ProvisionsFilter?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ProvisionsGetMaterialsImplCopyWith<$Res>
    implements $ProvisionsGetMaterialsCopyWith<$Res> {
  factory _$$ProvisionsGetMaterialsImplCopyWith(
          _$ProvisionsGetMaterialsImpl value,
          $Res Function(_$ProvisionsGetMaterialsImpl) then) =
      __$$ProvisionsGetMaterialsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({ProvisionsFilter? filter});
}

/// @nodoc
class __$$ProvisionsGetMaterialsImplCopyWithImpl<$Res>
    extends _$ProvisionsGetMaterialsCopyWithImpl<$Res,
        _$ProvisionsGetMaterialsImpl>
    implements _$$ProvisionsGetMaterialsImplCopyWith<$Res> {
  __$$ProvisionsGetMaterialsImplCopyWithImpl(
      _$ProvisionsGetMaterialsImpl _value,
      $Res Function(_$ProvisionsGetMaterialsImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProvisionsGetMaterials
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? filter = freezed,
  }) {
    return _then(_$ProvisionsGetMaterialsImpl(
      filter: freezed == filter
          ? _value.filter
          : filter // ignore: cast_nullable_to_non_nullable
              as ProvisionsFilter?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$ProvisionsGetMaterialsImpl implements _ProvisionsGetMaterials {
  const _$ProvisionsGetMaterialsImpl({this.filter});

  factory _$ProvisionsGetMaterialsImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProvisionsGetMaterialsImplFromJson(json);

  @override
  final ProvisionsFilter? filter;

  @override
  String toString() {
    return 'ProvisionsGetMaterials(filter: $filter)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProvisionsGetMaterialsImpl &&
            (identical(other.filter, filter) || other.filter == filter));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, filter);

  /// Create a copy of ProvisionsGetMaterials
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProvisionsGetMaterialsImplCopyWith<_$ProvisionsGetMaterialsImpl>
      get copyWith => __$$ProvisionsGetMaterialsImplCopyWithImpl<
          _$ProvisionsGetMaterialsImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProvisionsGetMaterialsImplToJson(
      this,
    );
  }
}

abstract class _ProvisionsGetMaterials implements ProvisionsGetMaterials {
  const factory _ProvisionsGetMaterials({final ProvisionsFilter? filter}) =
      _$ProvisionsGetMaterialsImpl;

  factory _ProvisionsGetMaterials.fromJson(Map<String, dynamic> json) =
      _$ProvisionsGetMaterialsImpl.fromJson;

  @override
  ProvisionsFilter? get filter;

  /// Create a copy of ProvisionsGetMaterials
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProvisionsGetMaterialsImplCopyWith<_$ProvisionsGetMaterialsImpl>
      get copyWith => throw _privateConstructorUsedError;
}

ProvisionsColumnOptionsInput _$ProvisionsColumnOptionsInputFromJson(
    Map<String, dynamic> json) {
  return _ProvisionsColumnOptionsInput.fromJson(json);
}

/// @nodoc
mixin _$ProvisionsColumnOptionsInput {
  String? get projectId => throw _privateConstructorUsedError;
  ProvisionColumn? get column => throw _privateConstructorUsedError;
  String? get search => throw _privateConstructorUsedError;
  ProvisionsFilter? get filterType => throw _privateConstructorUsedError;

  /// Serializes this ProvisionsColumnOptionsInput to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProvisionsColumnOptionsInput
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProvisionsColumnOptionsInputCopyWith<ProvisionsColumnOptionsInput>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProvisionsColumnOptionsInputCopyWith<$Res> {
  factory $ProvisionsColumnOptionsInputCopyWith(
          ProvisionsColumnOptionsInput value,
          $Res Function(ProvisionsColumnOptionsInput) then) =
      _$ProvisionsColumnOptionsInputCopyWithImpl<$Res,
          ProvisionsColumnOptionsInput>;
  @useResult
  $Res call(
      {String? projectId,
      ProvisionColumn? column,
      String? search,
      ProvisionsFilter? filterType});
}

/// @nodoc
class _$ProvisionsColumnOptionsInputCopyWithImpl<$Res,
        $Val extends ProvisionsColumnOptionsInput>
    implements $ProvisionsColumnOptionsInputCopyWith<$Res> {
  _$ProvisionsColumnOptionsInputCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProvisionsColumnOptionsInput
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? projectId = freezed,
    Object? column = freezed,
    Object? search = freezed,
    Object? filterType = freezed,
  }) {
    return _then(_value.copyWith(
      projectId: freezed == projectId
          ? _value.projectId
          : projectId // ignore: cast_nullable_to_non_nullable
              as String?,
      column: freezed == column
          ? _value.column
          : column // ignore: cast_nullable_to_non_nullable
              as ProvisionColumn?,
      search: freezed == search
          ? _value.search
          : search // ignore: cast_nullable_to_non_nullable
              as String?,
      filterType: freezed == filterType
          ? _value.filterType
          : filterType // ignore: cast_nullable_to_non_nullable
              as ProvisionsFilter?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ProvisionsColumnOptionsInputImplCopyWith<$Res>
    implements $ProvisionsColumnOptionsInputCopyWith<$Res> {
  factory _$$ProvisionsColumnOptionsInputImplCopyWith(
          _$ProvisionsColumnOptionsInputImpl value,
          $Res Function(_$ProvisionsColumnOptionsInputImpl) then) =
      __$$ProvisionsColumnOptionsInputImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? projectId,
      ProvisionColumn? column,
      String? search,
      ProvisionsFilter? filterType});
}

/// @nodoc
class __$$ProvisionsColumnOptionsInputImplCopyWithImpl<$Res>
    extends _$ProvisionsColumnOptionsInputCopyWithImpl<$Res,
        _$ProvisionsColumnOptionsInputImpl>
    implements _$$ProvisionsColumnOptionsInputImplCopyWith<$Res> {
  __$$ProvisionsColumnOptionsInputImplCopyWithImpl(
      _$ProvisionsColumnOptionsInputImpl _value,
      $Res Function(_$ProvisionsColumnOptionsInputImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProvisionsColumnOptionsInput
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? projectId = freezed,
    Object? column = freezed,
    Object? search = freezed,
    Object? filterType = freezed,
  }) {
    return _then(_$ProvisionsColumnOptionsInputImpl(
      projectId: freezed == projectId
          ? _value.projectId
          : projectId // ignore: cast_nullable_to_non_nullable
              as String?,
      column: freezed == column
          ? _value.column
          : column // ignore: cast_nullable_to_non_nullable
              as ProvisionColumn?,
      search: freezed == search
          ? _value.search
          : search // ignore: cast_nullable_to_non_nullable
              as String?,
      filterType: freezed == filterType
          ? _value.filterType
          : filterType // ignore: cast_nullable_to_non_nullable
              as ProvisionsFilter?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$ProvisionsColumnOptionsInputImpl
    implements _ProvisionsColumnOptionsInput {
  const _$ProvisionsColumnOptionsInputImpl(
      {this.projectId, this.column, this.search, this.filterType});

  factory _$ProvisionsColumnOptionsInputImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$ProvisionsColumnOptionsInputImplFromJson(json);

  @override
  final String? projectId;
  @override
  final ProvisionColumn? column;
  @override
  final String? search;
  @override
  final ProvisionsFilter? filterType;

  @override
  String toString() {
    return 'ProvisionsColumnOptionsInput(projectId: $projectId, column: $column, search: $search, filterType: $filterType)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProvisionsColumnOptionsInputImpl &&
            (identical(other.projectId, projectId) ||
                other.projectId == projectId) &&
            (identical(other.column, column) || other.column == column) &&
            (identical(other.search, search) || other.search == search) &&
            (identical(other.filterType, filterType) ||
                other.filterType == filterType));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, projectId, column, search, filterType);

  /// Create a copy of ProvisionsColumnOptionsInput
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProvisionsColumnOptionsInputImplCopyWith<
          _$ProvisionsColumnOptionsInputImpl>
      get copyWith => __$$ProvisionsColumnOptionsInputImplCopyWithImpl<
          _$ProvisionsColumnOptionsInputImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProvisionsColumnOptionsInputImplToJson(
      this,
    );
  }
}

abstract class _ProvisionsColumnOptionsInput
    implements ProvisionsColumnOptionsInput {
  const factory _ProvisionsColumnOptionsInput(
      {final String? projectId,
      final ProvisionColumn? column,
      final String? search,
      final ProvisionsFilter? filterType}) = _$ProvisionsColumnOptionsInputImpl;

  factory _ProvisionsColumnOptionsInput.fromJson(Map<String, dynamic> json) =
      _$ProvisionsColumnOptionsInputImpl.fromJson;

  @override
  String? get projectId;
  @override
  ProvisionColumn? get column;
  @override
  String? get search;
  @override
  ProvisionsFilter? get filterType;

  /// Create a copy of ProvisionsColumnOptionsInput
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProvisionsColumnOptionsInputImplCopyWith<
          _$ProvisionsColumnOptionsInputImpl>
      get copyWith => throw _privateConstructorUsedError;
}

ProvisionsColumnOptionsOutput _$ProvisionsColumnOptionsOutputFromJson(
    Map<String, dynamic> json) {
  return _ProvisionsColumnOptionsOutput.fromJson(json);
}

/// @nodoc
mixin _$ProvisionsColumnOptionsOutput {
  List<String>? get items => throw _privateConstructorUsedError;
  int? get totalItems => throw _privateConstructorUsedError;

  /// Serializes this ProvisionsColumnOptionsOutput to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProvisionsColumnOptionsOutput
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProvisionsColumnOptionsOutputCopyWith<ProvisionsColumnOptionsOutput>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProvisionsColumnOptionsOutputCopyWith<$Res> {
  factory $ProvisionsColumnOptionsOutputCopyWith(
          ProvisionsColumnOptionsOutput value,
          $Res Function(ProvisionsColumnOptionsOutput) then) =
      _$ProvisionsColumnOptionsOutputCopyWithImpl<$Res,
          ProvisionsColumnOptionsOutput>;
  @useResult
  $Res call({List<String>? items, int? totalItems});
}

/// @nodoc
class _$ProvisionsColumnOptionsOutputCopyWithImpl<$Res,
        $Val extends ProvisionsColumnOptionsOutput>
    implements $ProvisionsColumnOptionsOutputCopyWith<$Res> {
  _$ProvisionsColumnOptionsOutputCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProvisionsColumnOptionsOutput
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? items = freezed,
    Object? totalItems = freezed,
  }) {
    return _then(_value.copyWith(
      items: freezed == items
          ? _value.items
          : items // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      totalItems: freezed == totalItems
          ? _value.totalItems
          : totalItems // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ProvisionsColumnOptionsOutputImplCopyWith<$Res>
    implements $ProvisionsColumnOptionsOutputCopyWith<$Res> {
  factory _$$ProvisionsColumnOptionsOutputImplCopyWith(
          _$ProvisionsColumnOptionsOutputImpl value,
          $Res Function(_$ProvisionsColumnOptionsOutputImpl) then) =
      __$$ProvisionsColumnOptionsOutputImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<String>? items, int? totalItems});
}

/// @nodoc
class __$$ProvisionsColumnOptionsOutputImplCopyWithImpl<$Res>
    extends _$ProvisionsColumnOptionsOutputCopyWithImpl<$Res,
        _$ProvisionsColumnOptionsOutputImpl>
    implements _$$ProvisionsColumnOptionsOutputImplCopyWith<$Res> {
  __$$ProvisionsColumnOptionsOutputImplCopyWithImpl(
      _$ProvisionsColumnOptionsOutputImpl _value,
      $Res Function(_$ProvisionsColumnOptionsOutputImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProvisionsColumnOptionsOutput
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? items = freezed,
    Object? totalItems = freezed,
  }) {
    return _then(_$ProvisionsColumnOptionsOutputImpl(
      items: freezed == items
          ? _value._items
          : items // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      totalItems: freezed == totalItems
          ? _value.totalItems
          : totalItems // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$ProvisionsColumnOptionsOutputImpl
    implements _ProvisionsColumnOptionsOutput {
  const _$ProvisionsColumnOptionsOutputImpl(
      {final List<String>? items, this.totalItems})
      : _items = items;

  factory _$ProvisionsColumnOptionsOutputImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$ProvisionsColumnOptionsOutputImplFromJson(json);

  final List<String>? _items;
  @override
  List<String>? get items {
    final value = _items;
    if (value == null) return null;
    if (_items is EqualUnmodifiableListView) return _items;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final int? totalItems;

  @override
  String toString() {
    return 'ProvisionsColumnOptionsOutput(items: $items, totalItems: $totalItems)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProvisionsColumnOptionsOutputImpl &&
            const DeepCollectionEquality().equals(other._items, _items) &&
            (identical(other.totalItems, totalItems) ||
                other.totalItems == totalItems));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_items), totalItems);

  /// Create a copy of ProvisionsColumnOptionsOutput
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProvisionsColumnOptionsOutputImplCopyWith<
          _$ProvisionsColumnOptionsOutputImpl>
      get copyWith => __$$ProvisionsColumnOptionsOutputImplCopyWithImpl<
          _$ProvisionsColumnOptionsOutputImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProvisionsColumnOptionsOutputImplToJson(
      this,
    );
  }
}

abstract class _ProvisionsColumnOptionsOutput
    implements ProvisionsColumnOptionsOutput {
  const factory _ProvisionsColumnOptionsOutput(
      {final List<String>? items,
      final int? totalItems}) = _$ProvisionsColumnOptionsOutputImpl;

  factory _ProvisionsColumnOptionsOutput.fromJson(Map<String, dynamic> json) =
      _$ProvisionsColumnOptionsOutputImpl.fromJson;

  @override
  List<String>? get items;
  @override
  int? get totalItems;

  /// Create a copy of ProvisionsColumnOptionsOutput
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProvisionsColumnOptionsOutputImplCopyWith<
          _$ProvisionsColumnOptionsOutputImpl>
      get copyWith => throw _privateConstructorUsedError;
}

ProvisionCreateLotInput _$ProvisionCreateLotInputFromJson(
    Map<String, dynamic> json) {
  return _ProvisionCreateLotInput.fromJson(json);
}

/// @nodoc
mixin _$ProvisionCreateLotInput {
  String? get projectId => throw _privateConstructorUsedError;
  String? get lotName => throw _privateConstructorUsedError;
  List<String>? get productIds => throw _privateConstructorUsedError;
  String? get plannedTenderCompletionDate => throw _privateConstructorUsedError;

  /// Serializes this ProvisionCreateLotInput to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProvisionCreateLotInput
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProvisionCreateLotInputCopyWith<ProvisionCreateLotInput> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProvisionCreateLotInputCopyWith<$Res> {
  factory $ProvisionCreateLotInputCopyWith(ProvisionCreateLotInput value,
          $Res Function(ProvisionCreateLotInput) then) =
      _$ProvisionCreateLotInputCopyWithImpl<$Res, ProvisionCreateLotInput>;
  @useResult
  $Res call(
      {String? projectId,
      String? lotName,
      List<String>? productIds,
      String? plannedTenderCompletionDate});
}

/// @nodoc
class _$ProvisionCreateLotInputCopyWithImpl<$Res,
        $Val extends ProvisionCreateLotInput>
    implements $ProvisionCreateLotInputCopyWith<$Res> {
  _$ProvisionCreateLotInputCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProvisionCreateLotInput
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? projectId = freezed,
    Object? lotName = freezed,
    Object? productIds = freezed,
    Object? plannedTenderCompletionDate = freezed,
  }) {
    return _then(_value.copyWith(
      projectId: freezed == projectId
          ? _value.projectId
          : projectId // ignore: cast_nullable_to_non_nullable
              as String?,
      lotName: freezed == lotName
          ? _value.lotName
          : lotName // ignore: cast_nullable_to_non_nullable
              as String?,
      productIds: freezed == productIds
          ? _value.productIds
          : productIds // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      plannedTenderCompletionDate: freezed == plannedTenderCompletionDate
          ? _value.plannedTenderCompletionDate
          : plannedTenderCompletionDate // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ProvisionCreateLotInputImplCopyWith<$Res>
    implements $ProvisionCreateLotInputCopyWith<$Res> {
  factory _$$ProvisionCreateLotInputImplCopyWith(
          _$ProvisionCreateLotInputImpl value,
          $Res Function(_$ProvisionCreateLotInputImpl) then) =
      __$$ProvisionCreateLotInputImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? projectId,
      String? lotName,
      List<String>? productIds,
      String? plannedTenderCompletionDate});
}

/// @nodoc
class __$$ProvisionCreateLotInputImplCopyWithImpl<$Res>
    extends _$ProvisionCreateLotInputCopyWithImpl<$Res,
        _$ProvisionCreateLotInputImpl>
    implements _$$ProvisionCreateLotInputImplCopyWith<$Res> {
  __$$ProvisionCreateLotInputImplCopyWithImpl(
      _$ProvisionCreateLotInputImpl _value,
      $Res Function(_$ProvisionCreateLotInputImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProvisionCreateLotInput
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? projectId = freezed,
    Object? lotName = freezed,
    Object? productIds = freezed,
    Object? plannedTenderCompletionDate = freezed,
  }) {
    return _then(_$ProvisionCreateLotInputImpl(
      projectId: freezed == projectId
          ? _value.projectId
          : projectId // ignore: cast_nullable_to_non_nullable
              as String?,
      lotName: freezed == lotName
          ? _value.lotName
          : lotName // ignore: cast_nullable_to_non_nullable
              as String?,
      productIds: freezed == productIds
          ? _value._productIds
          : productIds // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      plannedTenderCompletionDate: freezed == plannedTenderCompletionDate
          ? _value.plannedTenderCompletionDate
          : plannedTenderCompletionDate // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$ProvisionCreateLotInputImpl implements _ProvisionCreateLotInput {
  _$ProvisionCreateLotInputImpl(
      {this.projectId,
      this.lotName,
      final List<String>? productIds,
      this.plannedTenderCompletionDate})
      : _productIds = productIds;

  factory _$ProvisionCreateLotInputImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProvisionCreateLotInputImplFromJson(json);

  @override
  final String? projectId;
  @override
  final String? lotName;
  final List<String>? _productIds;
  @override
  List<String>? get productIds {
    final value = _productIds;
    if (value == null) return null;
    if (_productIds is EqualUnmodifiableListView) return _productIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? plannedTenderCompletionDate;

  @override
  String toString() {
    return 'ProvisionCreateLotInput(projectId: $projectId, lotName: $lotName, productIds: $productIds, plannedTenderCompletionDate: $plannedTenderCompletionDate)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProvisionCreateLotInputImpl &&
            (identical(other.projectId, projectId) ||
                other.projectId == projectId) &&
            (identical(other.lotName, lotName) || other.lotName == lotName) &&
            const DeepCollectionEquality()
                .equals(other._productIds, _productIds) &&
            (identical(other.plannedTenderCompletionDate,
                    plannedTenderCompletionDate) ||
                other.plannedTenderCompletionDate ==
                    plannedTenderCompletionDate));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      projectId,
      lotName,
      const DeepCollectionEquality().hash(_productIds),
      plannedTenderCompletionDate);

  /// Create a copy of ProvisionCreateLotInput
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProvisionCreateLotInputImplCopyWith<_$ProvisionCreateLotInputImpl>
      get copyWith => __$$ProvisionCreateLotInputImplCopyWithImpl<
          _$ProvisionCreateLotInputImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProvisionCreateLotInputImplToJson(
      this,
    );
  }
}

abstract class _ProvisionCreateLotInput implements ProvisionCreateLotInput {
  factory _ProvisionCreateLotInput(
          {final String? projectId,
          final String? lotName,
          final List<String>? productIds,
          final String? plannedTenderCompletionDate}) =
      _$ProvisionCreateLotInputImpl;

  factory _ProvisionCreateLotInput.fromJson(Map<String, dynamic> json) =
      _$ProvisionCreateLotInputImpl.fromJson;

  @override
  String? get projectId;
  @override
  String? get lotName;
  @override
  List<String>? get productIds;
  @override
  String? get plannedTenderCompletionDate;

  /// Create a copy of ProvisionCreateLotInput
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProvisionCreateLotInputImplCopyWith<_$ProvisionCreateLotInputImpl>
      get copyWith => throw _privateConstructorUsedError;
}

ProvisionAddToLotInput _$ProvisionAddToLotInputFromJson(
    Map<String, dynamic> json) {
  return _ProvisionAddToLotInput.fromJson(json);
}

/// @nodoc
mixin _$ProvisionAddToLotInput {
// String? projectId,
  List<int>? get lotNumbers => throw _privateConstructorUsedError;
  List<String>? get productIds => throw _privateConstructorUsedError;

  /// Serializes this ProvisionAddToLotInput to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProvisionAddToLotInput
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProvisionAddToLotInputCopyWith<ProvisionAddToLotInput> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProvisionAddToLotInputCopyWith<$Res> {
  factory $ProvisionAddToLotInputCopyWith(ProvisionAddToLotInput value,
          $Res Function(ProvisionAddToLotInput) then) =
      _$ProvisionAddToLotInputCopyWithImpl<$Res, ProvisionAddToLotInput>;
  @useResult
  $Res call({List<int>? lotNumbers, List<String>? productIds});
}

/// @nodoc
class _$ProvisionAddToLotInputCopyWithImpl<$Res,
        $Val extends ProvisionAddToLotInput>
    implements $ProvisionAddToLotInputCopyWith<$Res> {
  _$ProvisionAddToLotInputCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProvisionAddToLotInput
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? lotNumbers = freezed,
    Object? productIds = freezed,
  }) {
    return _then(_value.copyWith(
      lotNumbers: freezed == lotNumbers
          ? _value.lotNumbers
          : lotNumbers // ignore: cast_nullable_to_non_nullable
              as List<int>?,
      productIds: freezed == productIds
          ? _value.productIds
          : productIds // ignore: cast_nullable_to_non_nullable
              as List<String>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ProvisionAddToLotInputImplCopyWith<$Res>
    implements $ProvisionAddToLotInputCopyWith<$Res> {
  factory _$$ProvisionAddToLotInputImplCopyWith(
          _$ProvisionAddToLotInputImpl value,
          $Res Function(_$ProvisionAddToLotInputImpl) then) =
      __$$ProvisionAddToLotInputImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<int>? lotNumbers, List<String>? productIds});
}

/// @nodoc
class __$$ProvisionAddToLotInputImplCopyWithImpl<$Res>
    extends _$ProvisionAddToLotInputCopyWithImpl<$Res,
        _$ProvisionAddToLotInputImpl>
    implements _$$ProvisionAddToLotInputImplCopyWith<$Res> {
  __$$ProvisionAddToLotInputImplCopyWithImpl(
      _$ProvisionAddToLotInputImpl _value,
      $Res Function(_$ProvisionAddToLotInputImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProvisionAddToLotInput
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? lotNumbers = freezed,
    Object? productIds = freezed,
  }) {
    return _then(_$ProvisionAddToLotInputImpl(
      lotNumbers: freezed == lotNumbers
          ? _value._lotNumbers
          : lotNumbers // ignore: cast_nullable_to_non_nullable
              as List<int>?,
      productIds: freezed == productIds
          ? _value._productIds
          : productIds // ignore: cast_nullable_to_non_nullable
              as List<String>?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$ProvisionAddToLotInputImpl implements _ProvisionAddToLotInput {
  _$ProvisionAddToLotInputImpl(
      {final List<int>? lotNumbers, final List<String>? productIds})
      : _lotNumbers = lotNumbers,
        _productIds = productIds;

  factory _$ProvisionAddToLotInputImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProvisionAddToLotInputImplFromJson(json);

// String? projectId,
  final List<int>? _lotNumbers;
// String? projectId,
  @override
  List<int>? get lotNumbers {
    final value = _lotNumbers;
    if (value == null) return null;
    if (_lotNumbers is EqualUnmodifiableListView) return _lotNumbers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<String>? _productIds;
  @override
  List<String>? get productIds {
    final value = _productIds;
    if (value == null) return null;
    if (_productIds is EqualUnmodifiableListView) return _productIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'ProvisionAddToLotInput(lotNumbers: $lotNumbers, productIds: $productIds)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProvisionAddToLotInputImpl &&
            const DeepCollectionEquality()
                .equals(other._lotNumbers, _lotNumbers) &&
            const DeepCollectionEquality()
                .equals(other._productIds, _productIds));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_lotNumbers),
      const DeepCollectionEquality().hash(_productIds));

  /// Create a copy of ProvisionAddToLotInput
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProvisionAddToLotInputImplCopyWith<_$ProvisionAddToLotInputImpl>
      get copyWith => __$$ProvisionAddToLotInputImplCopyWithImpl<
          _$ProvisionAddToLotInputImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProvisionAddToLotInputImplToJson(
      this,
    );
  }
}

abstract class _ProvisionAddToLotInput implements ProvisionAddToLotInput {
  factory _ProvisionAddToLotInput(
      {final List<int>? lotNumbers,
      final List<String>? productIds}) = _$ProvisionAddToLotInputImpl;

  factory _ProvisionAddToLotInput.fromJson(Map<String, dynamic> json) =
      _$ProvisionAddToLotInputImpl.fromJson;

// String? projectId,
  @override
  List<int>? get lotNumbers;
  @override
  List<String>? get productIds;

  /// Create a copy of ProvisionAddToLotInput
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProvisionAddToLotInputImplCopyWith<_$ProvisionAddToLotInputImpl>
      get copyWith => throw _privateConstructorUsedError;
}

ProvisionCreateContractInput _$ProvisionCreateContractInputFromJson(
    Map<String, dynamic> json) {
  return _ProvisionCreateContractInput.fromJson(json);
}

/// @nodoc
mixin _$ProvisionCreateContractInput {
  List<String> get productIds =>
      throw _privateConstructorUsedError; // ID продуктов
// required String contractNumber, // Номер договора
  @JsonKey(name: 'contractDate')
  String get contractDate =>
      throw _privateConstructorUsedError; // Дата заключения
  @JsonKey(name: 'contractStartDate')
  String? get contractStartDate =>
      throw _privateConstructorUsedError; // Дата начала
  @JsonKey(name: 'contractEndDate')
  String? get contractEndDate =>
      throw _privateConstructorUsedError; // Дата окончания
  String? get supplierId => throw _privateConstructorUsedError; // ID поставщика
  @JsonKey(name: 'plannedDeliveryDate')
  String? get plannedDeliveryDate =>
      throw _privateConstructorUsedError; // Плановая дата поставки
  double get contractPrice =>
      throw _privateConstructorUsedError; // Общая сумма контракта
  @JsonKey(name: 'productDetails')
  List<ProductDetail>? get productDetails =>
      throw _privateConstructorUsedError; // Детали продуктов
  @JsonKey(name: 'paymentDetails')
  List<PaymentDetail>? get paymentDetails =>
      throw _privateConstructorUsedError; // Детали оплат
  ProvisionsFilter? get filterType => throw _privateConstructorUsedError;
  String? get notes => throw _privateConstructorUsedError;

  /// Serializes this ProvisionCreateContractInput to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProvisionCreateContractInput
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProvisionCreateContractInputCopyWith<ProvisionCreateContractInput>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProvisionCreateContractInputCopyWith<$Res> {
  factory $ProvisionCreateContractInputCopyWith(
          ProvisionCreateContractInput value,
          $Res Function(ProvisionCreateContractInput) then) =
      _$ProvisionCreateContractInputCopyWithImpl<$Res,
          ProvisionCreateContractInput>;
  @useResult
  $Res call(
      {List<String> productIds,
      @JsonKey(name: 'contractDate') String contractDate,
      @JsonKey(name: 'contractStartDate') String? contractStartDate,
      @JsonKey(name: 'contractEndDate') String? contractEndDate,
      String? supplierId,
      @JsonKey(name: 'plannedDeliveryDate') String? plannedDeliveryDate,
      double contractPrice,
      @JsonKey(name: 'productDetails') List<ProductDetail>? productDetails,
      @JsonKey(name: 'paymentDetails') List<PaymentDetail>? paymentDetails,
      ProvisionsFilter? filterType,
      String? notes});
}

/// @nodoc
class _$ProvisionCreateContractInputCopyWithImpl<$Res,
        $Val extends ProvisionCreateContractInput>
    implements $ProvisionCreateContractInputCopyWith<$Res> {
  _$ProvisionCreateContractInputCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProvisionCreateContractInput
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? productIds = null,
    Object? contractDate = null,
    Object? contractStartDate = freezed,
    Object? contractEndDate = freezed,
    Object? supplierId = freezed,
    Object? plannedDeliveryDate = freezed,
    Object? contractPrice = null,
    Object? productDetails = freezed,
    Object? paymentDetails = freezed,
    Object? filterType = freezed,
    Object? notes = freezed,
  }) {
    return _then(_value.copyWith(
      productIds: null == productIds
          ? _value.productIds
          : productIds // ignore: cast_nullable_to_non_nullable
              as List<String>,
      contractDate: null == contractDate
          ? _value.contractDate
          : contractDate // ignore: cast_nullable_to_non_nullable
              as String,
      contractStartDate: freezed == contractStartDate
          ? _value.contractStartDate
          : contractStartDate // ignore: cast_nullable_to_non_nullable
              as String?,
      contractEndDate: freezed == contractEndDate
          ? _value.contractEndDate
          : contractEndDate // ignore: cast_nullable_to_non_nullable
              as String?,
      supplierId: freezed == supplierId
          ? _value.supplierId
          : supplierId // ignore: cast_nullable_to_non_nullable
              as String?,
      plannedDeliveryDate: freezed == plannedDeliveryDate
          ? _value.plannedDeliveryDate
          : plannedDeliveryDate // ignore: cast_nullable_to_non_nullable
              as String?,
      contractPrice: null == contractPrice
          ? _value.contractPrice
          : contractPrice // ignore: cast_nullable_to_non_nullable
              as double,
      productDetails: freezed == productDetails
          ? _value.productDetails
          : productDetails // ignore: cast_nullable_to_non_nullable
              as List<ProductDetail>?,
      paymentDetails: freezed == paymentDetails
          ? _value.paymentDetails
          : paymentDetails // ignore: cast_nullable_to_non_nullable
              as List<PaymentDetail>?,
      filterType: freezed == filterType
          ? _value.filterType
          : filterType // ignore: cast_nullable_to_non_nullable
              as ProvisionsFilter?,
      notes: freezed == notes
          ? _value.notes
          : notes // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ProvisionCreateContractInputImplCopyWith<$Res>
    implements $ProvisionCreateContractInputCopyWith<$Res> {
  factory _$$ProvisionCreateContractInputImplCopyWith(
          _$ProvisionCreateContractInputImpl value,
          $Res Function(_$ProvisionCreateContractInputImpl) then) =
      __$$ProvisionCreateContractInputImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<String> productIds,
      @JsonKey(name: 'contractDate') String contractDate,
      @JsonKey(name: 'contractStartDate') String? contractStartDate,
      @JsonKey(name: 'contractEndDate') String? contractEndDate,
      String? supplierId,
      @JsonKey(name: 'plannedDeliveryDate') String? plannedDeliveryDate,
      double contractPrice,
      @JsonKey(name: 'productDetails') List<ProductDetail>? productDetails,
      @JsonKey(name: 'paymentDetails') List<PaymentDetail>? paymentDetails,
      ProvisionsFilter? filterType,
      String? notes});
}

/// @nodoc
class __$$ProvisionCreateContractInputImplCopyWithImpl<$Res>
    extends _$ProvisionCreateContractInputCopyWithImpl<$Res,
        _$ProvisionCreateContractInputImpl>
    implements _$$ProvisionCreateContractInputImplCopyWith<$Res> {
  __$$ProvisionCreateContractInputImplCopyWithImpl(
      _$ProvisionCreateContractInputImpl _value,
      $Res Function(_$ProvisionCreateContractInputImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProvisionCreateContractInput
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? productIds = null,
    Object? contractDate = null,
    Object? contractStartDate = freezed,
    Object? contractEndDate = freezed,
    Object? supplierId = freezed,
    Object? plannedDeliveryDate = freezed,
    Object? contractPrice = null,
    Object? productDetails = freezed,
    Object? paymentDetails = freezed,
    Object? filterType = freezed,
    Object? notes = freezed,
  }) {
    return _then(_$ProvisionCreateContractInputImpl(
      productIds: null == productIds
          ? _value._productIds
          : productIds // ignore: cast_nullable_to_non_nullable
              as List<String>,
      contractDate: null == contractDate
          ? _value.contractDate
          : contractDate // ignore: cast_nullable_to_non_nullable
              as String,
      contractStartDate: freezed == contractStartDate
          ? _value.contractStartDate
          : contractStartDate // ignore: cast_nullable_to_non_nullable
              as String?,
      contractEndDate: freezed == contractEndDate
          ? _value.contractEndDate
          : contractEndDate // ignore: cast_nullable_to_non_nullable
              as String?,
      supplierId: freezed == supplierId
          ? _value.supplierId
          : supplierId // ignore: cast_nullable_to_non_nullable
              as String?,
      plannedDeliveryDate: freezed == plannedDeliveryDate
          ? _value.plannedDeliveryDate
          : plannedDeliveryDate // ignore: cast_nullable_to_non_nullable
              as String?,
      contractPrice: null == contractPrice
          ? _value.contractPrice
          : contractPrice // ignore: cast_nullable_to_non_nullable
              as double,
      productDetails: freezed == productDetails
          ? _value._productDetails
          : productDetails // ignore: cast_nullable_to_non_nullable
              as List<ProductDetail>?,
      paymentDetails: freezed == paymentDetails
          ? _value._paymentDetails
          : paymentDetails // ignore: cast_nullable_to_non_nullable
              as List<PaymentDetail>?,
      filterType: freezed == filterType
          ? _value.filterType
          : filterType // ignore: cast_nullable_to_non_nullable
              as ProvisionsFilter?,
      notes: freezed == notes
          ? _value.notes
          : notes // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$ProvisionCreateContractInputImpl
    implements _ProvisionCreateContractInput {
  _$ProvisionCreateContractInputImpl(
      {required final List<String> productIds,
      @JsonKey(name: 'contractDate') required this.contractDate,
      @JsonKey(name: 'contractStartDate') this.contractStartDate,
      @JsonKey(name: 'contractEndDate') this.contractEndDate,
      this.supplierId,
      @JsonKey(name: 'plannedDeliveryDate') this.plannedDeliveryDate,
      required this.contractPrice,
      @JsonKey(name: 'productDetails')
      final List<ProductDetail>? productDetails,
      @JsonKey(name: 'paymentDetails')
      final List<PaymentDetail>? paymentDetails,
      this.filterType,
      this.notes})
      : _productIds = productIds,
        _productDetails = productDetails,
        _paymentDetails = paymentDetails;

  factory _$ProvisionCreateContractInputImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$ProvisionCreateContractInputImplFromJson(json);

  final List<String> _productIds;
  @override
  List<String> get productIds {
    if (_productIds is EqualUnmodifiableListView) return _productIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_productIds);
  }

// ID продуктов
// required String contractNumber, // Номер договора
  @override
  @JsonKey(name: 'contractDate')
  final String contractDate;
// Дата заключения
  @override
  @JsonKey(name: 'contractStartDate')
  final String? contractStartDate;
// Дата начала
  @override
  @JsonKey(name: 'contractEndDate')
  final String? contractEndDate;
// Дата окончания
  @override
  final String? supplierId;
// ID поставщика
  @override
  @JsonKey(name: 'plannedDeliveryDate')
  final String? plannedDeliveryDate;
// Плановая дата поставки
  @override
  final double contractPrice;
// Общая сумма контракта
  final List<ProductDetail>? _productDetails;
// Общая сумма контракта
  @override
  @JsonKey(name: 'productDetails')
  List<ProductDetail>? get productDetails {
    final value = _productDetails;
    if (value == null) return null;
    if (_productDetails is EqualUnmodifiableListView) return _productDetails;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

// Детали продуктов
  final List<PaymentDetail>? _paymentDetails;
// Детали продуктов
  @override
  @JsonKey(name: 'paymentDetails')
  List<PaymentDetail>? get paymentDetails {
    final value = _paymentDetails;
    if (value == null) return null;
    if (_paymentDetails is EqualUnmodifiableListView) return _paymentDetails;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

// Детали оплат
  @override
  final ProvisionsFilter? filterType;
  @override
  final String? notes;

  @override
  String toString() {
    return 'ProvisionCreateContractInput(productIds: $productIds, contractDate: $contractDate, contractStartDate: $contractStartDate, contractEndDate: $contractEndDate, supplierId: $supplierId, plannedDeliveryDate: $plannedDeliveryDate, contractPrice: $contractPrice, productDetails: $productDetails, paymentDetails: $paymentDetails, filterType: $filterType, notes: $notes)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProvisionCreateContractInputImpl &&
            const DeepCollectionEquality()
                .equals(other._productIds, _productIds) &&
            (identical(other.contractDate, contractDate) ||
                other.contractDate == contractDate) &&
            (identical(other.contractStartDate, contractStartDate) ||
                other.contractStartDate == contractStartDate) &&
            (identical(other.contractEndDate, contractEndDate) ||
                other.contractEndDate == contractEndDate) &&
            (identical(other.supplierId, supplierId) ||
                other.supplierId == supplierId) &&
            (identical(other.plannedDeliveryDate, plannedDeliveryDate) ||
                other.plannedDeliveryDate == plannedDeliveryDate) &&
            (identical(other.contractPrice, contractPrice) ||
                other.contractPrice == contractPrice) &&
            const DeepCollectionEquality()
                .equals(other._productDetails, _productDetails) &&
            const DeepCollectionEquality()
                .equals(other._paymentDetails, _paymentDetails) &&
            (identical(other.filterType, filterType) ||
                other.filterType == filterType) &&
            (identical(other.notes, notes) || other.notes == notes));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_productIds),
      contractDate,
      contractStartDate,
      contractEndDate,
      supplierId,
      plannedDeliveryDate,
      contractPrice,
      const DeepCollectionEquality().hash(_productDetails),
      const DeepCollectionEquality().hash(_paymentDetails),
      filterType,
      notes);

  /// Create a copy of ProvisionCreateContractInput
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProvisionCreateContractInputImplCopyWith<
          _$ProvisionCreateContractInputImpl>
      get copyWith => __$$ProvisionCreateContractInputImplCopyWithImpl<
          _$ProvisionCreateContractInputImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProvisionCreateContractInputImplToJson(
      this,
    );
  }
}

abstract class _ProvisionCreateContractInput
    implements ProvisionCreateContractInput {
  factory _ProvisionCreateContractInput(
      {required final List<String> productIds,
      @JsonKey(name: 'contractDate') required final String contractDate,
      @JsonKey(name: 'contractStartDate') final String? contractStartDate,
      @JsonKey(name: 'contractEndDate') final String? contractEndDate,
      final String? supplierId,
      @JsonKey(name: 'plannedDeliveryDate') final String? plannedDeliveryDate,
      required final double contractPrice,
      @JsonKey(name: 'productDetails')
      final List<ProductDetail>? productDetails,
      @JsonKey(name: 'paymentDetails')
      final List<PaymentDetail>? paymentDetails,
      final ProvisionsFilter? filterType,
      final String? notes}) = _$ProvisionCreateContractInputImpl;

  factory _ProvisionCreateContractInput.fromJson(Map<String, dynamic> json) =
      _$ProvisionCreateContractInputImpl.fromJson;

  @override
  List<String> get productIds; // ID продуктов
// required String contractNumber, // Номер договора
  @override
  @JsonKey(name: 'contractDate')
  String get contractDate; // Дата заключения
  @override
  @JsonKey(name: 'contractStartDate')
  String? get contractStartDate; // Дата начала
  @override
  @JsonKey(name: 'contractEndDate')
  String? get contractEndDate; // Дата окончания
  @override
  String? get supplierId; // ID поставщика
  @override
  @JsonKey(name: 'plannedDeliveryDate')
  String? get plannedDeliveryDate; // Плановая дата поставки
  @override
  double get contractPrice; // Общая сумма контракта
  @override
  @JsonKey(name: 'productDetails')
  List<ProductDetail>? get productDetails; // Детали продуктов
  @override
  @JsonKey(name: 'paymentDetails')
  List<PaymentDetail>? get paymentDetails; // Детали оплат
  @override
  ProvisionsFilter? get filterType;
  @override
  String? get notes;

  /// Create a copy of ProvisionCreateContractInput
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProvisionCreateContractInputImplCopyWith<
          _$ProvisionCreateContractInputImpl>
      get copyWith => throw _privateConstructorUsedError;
}

ProductDetail _$ProductDetailFromJson(Map<String, dynamic> json) {
  return _ProductDetail.fromJson(json);
}

/// @nodoc
mixin _$ProductDetail {
  String? get productId => throw _privateConstructorUsedError; // ID продукта
  double? get price => throw _privateConstructorUsedError; // Цена (>= 0)
  UnitType? get unit => throw _privateConstructorUsedError; // Единица измерения
  List<Distribution>? get distributions =>
      throw _privateConstructorUsedError; // Распределения по складам/датам
  String? get notes => throw _privateConstructorUsedError;

  /// Serializes this ProductDetail to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProductDetail
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProductDetailCopyWith<ProductDetail> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProductDetailCopyWith<$Res> {
  factory $ProductDetailCopyWith(
          ProductDetail value, $Res Function(ProductDetail) then) =
      _$ProductDetailCopyWithImpl<$Res, ProductDetail>;
  @useResult
  $Res call(
      {String? productId,
      double? price,
      UnitType? unit,
      List<Distribution>? distributions,
      String? notes});
}

/// @nodoc
class _$ProductDetailCopyWithImpl<$Res, $Val extends ProductDetail>
    implements $ProductDetailCopyWith<$Res> {
  _$ProductDetailCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProductDetail
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? productId = freezed,
    Object? price = freezed,
    Object? unit = freezed,
    Object? distributions = freezed,
    Object? notes = freezed,
  }) {
    return _then(_value.copyWith(
      productId: freezed == productId
          ? _value.productId
          : productId // ignore: cast_nullable_to_non_nullable
              as String?,
      price: freezed == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as double?,
      unit: freezed == unit
          ? _value.unit
          : unit // ignore: cast_nullable_to_non_nullable
              as UnitType?,
      distributions: freezed == distributions
          ? _value.distributions
          : distributions // ignore: cast_nullable_to_non_nullable
              as List<Distribution>?,
      notes: freezed == notes
          ? _value.notes
          : notes // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ProductDetailImplCopyWith<$Res>
    implements $ProductDetailCopyWith<$Res> {
  factory _$$ProductDetailImplCopyWith(
          _$ProductDetailImpl value, $Res Function(_$ProductDetailImpl) then) =
      __$$ProductDetailImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? productId,
      double? price,
      UnitType? unit,
      List<Distribution>? distributions,
      String? notes});
}

/// @nodoc
class __$$ProductDetailImplCopyWithImpl<$Res>
    extends _$ProductDetailCopyWithImpl<$Res, _$ProductDetailImpl>
    implements _$$ProductDetailImplCopyWith<$Res> {
  __$$ProductDetailImplCopyWithImpl(
      _$ProductDetailImpl _value, $Res Function(_$ProductDetailImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProductDetail
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? productId = freezed,
    Object? price = freezed,
    Object? unit = freezed,
    Object? distributions = freezed,
    Object? notes = freezed,
  }) {
    return _then(_$ProductDetailImpl(
      productId: freezed == productId
          ? _value.productId
          : productId // ignore: cast_nullable_to_non_nullable
              as String?,
      price: freezed == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as double?,
      unit: freezed == unit
          ? _value.unit
          : unit // ignore: cast_nullable_to_non_nullable
              as UnitType?,
      distributions: freezed == distributions
          ? _value._distributions
          : distributions // ignore: cast_nullable_to_non_nullable
              as List<Distribution>?,
      notes: freezed == notes
          ? _value.notes
          : notes // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$ProductDetailImpl implements _ProductDetail {
  _$ProductDetailImpl(
      {this.productId,
      this.price,
      this.unit,
      final List<Distribution>? distributions,
      this.notes})
      : _distributions = distributions;

  factory _$ProductDetailImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProductDetailImplFromJson(json);

  @override
  final String? productId;
// ID продукта
  @override
  final double? price;
// Цена (>= 0)
  @override
  final UnitType? unit;
// Единица измерения
  final List<Distribution>? _distributions;
// Единица измерения
  @override
  List<Distribution>? get distributions {
    final value = _distributions;
    if (value == null) return null;
    if (_distributions is EqualUnmodifiableListView) return _distributions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

// Распределения по складам/датам
  @override
  final String? notes;

  @override
  String toString() {
    return 'ProductDetail(productId: $productId, price: $price, unit: $unit, distributions: $distributions, notes: $notes)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProductDetailImpl &&
            (identical(other.productId, productId) ||
                other.productId == productId) &&
            (identical(other.price, price) || other.price == price) &&
            (identical(other.unit, unit) || other.unit == unit) &&
            const DeepCollectionEquality()
                .equals(other._distributions, _distributions) &&
            (identical(other.notes, notes) || other.notes == notes));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, productId, price, unit,
      const DeepCollectionEquality().hash(_distributions), notes);

  /// Create a copy of ProductDetail
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProductDetailImplCopyWith<_$ProductDetailImpl> get copyWith =>
      __$$ProductDetailImplCopyWithImpl<_$ProductDetailImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProductDetailImplToJson(
      this,
    );
  }
}

abstract class _ProductDetail implements ProductDetail {
  factory _ProductDetail(
      {final String? productId,
      final double? price,
      final UnitType? unit,
      final List<Distribution>? distributions,
      final String? notes}) = _$ProductDetailImpl;

  factory _ProductDetail.fromJson(Map<String, dynamic> json) =
      _$ProductDetailImpl.fromJson;

  @override
  String? get productId; // ID продукта
  @override
  double? get price; // Цена (>= 0)
  @override
  UnitType? get unit; // Единица измерения
  @override
  List<Distribution>? get distributions; // Распределения по складам/датам
  @override
  String? get notes;

  /// Create a copy of ProductDetail
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProductDetailImplCopyWith<_$ProductDetailImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

Distribution _$DistributionFromJson(Map<String, dynamic> json) {
  return _Distribution.fromJson(json);
}

/// @nodoc
mixin _$Distribution {
  String? get warehouseId => throw _privateConstructorUsedError; // ID склада
  double? get quantity =>
      throw _privateConstructorUsedError; // Количество (>= 0)
  String? get deliveryDate =>
      throw _privateConstructorUsedError; // Дата поставки (YYYY-MM-DD)
  String? get notes => throw _privateConstructorUsedError;

  /// Serializes this Distribution to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Distribution
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DistributionCopyWith<Distribution> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DistributionCopyWith<$Res> {
  factory $DistributionCopyWith(
          Distribution value, $Res Function(Distribution) then) =
      _$DistributionCopyWithImpl<$Res, Distribution>;
  @useResult
  $Res call(
      {String? warehouseId,
      double? quantity,
      String? deliveryDate,
      String? notes});
}

/// @nodoc
class _$DistributionCopyWithImpl<$Res, $Val extends Distribution>
    implements $DistributionCopyWith<$Res> {
  _$DistributionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Distribution
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? warehouseId = freezed,
    Object? quantity = freezed,
    Object? deliveryDate = freezed,
    Object? notes = freezed,
  }) {
    return _then(_value.copyWith(
      warehouseId: freezed == warehouseId
          ? _value.warehouseId
          : warehouseId // ignore: cast_nullable_to_non_nullable
              as String?,
      quantity: freezed == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as double?,
      deliveryDate: freezed == deliveryDate
          ? _value.deliveryDate
          : deliveryDate // ignore: cast_nullable_to_non_nullable
              as String?,
      notes: freezed == notes
          ? _value.notes
          : notes // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$DistributionImplCopyWith<$Res>
    implements $DistributionCopyWith<$Res> {
  factory _$$DistributionImplCopyWith(
          _$DistributionImpl value, $Res Function(_$DistributionImpl) then) =
      __$$DistributionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? warehouseId,
      double? quantity,
      String? deliveryDate,
      String? notes});
}

/// @nodoc
class __$$DistributionImplCopyWithImpl<$Res>
    extends _$DistributionCopyWithImpl<$Res, _$DistributionImpl>
    implements _$$DistributionImplCopyWith<$Res> {
  __$$DistributionImplCopyWithImpl(
      _$DistributionImpl _value, $Res Function(_$DistributionImpl) _then)
      : super(_value, _then);

  /// Create a copy of Distribution
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? warehouseId = freezed,
    Object? quantity = freezed,
    Object? deliveryDate = freezed,
    Object? notes = freezed,
  }) {
    return _then(_$DistributionImpl(
      warehouseId: freezed == warehouseId
          ? _value.warehouseId
          : warehouseId // ignore: cast_nullable_to_non_nullable
              as String?,
      quantity: freezed == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as double?,
      deliveryDate: freezed == deliveryDate
          ? _value.deliveryDate
          : deliveryDate // ignore: cast_nullable_to_non_nullable
              as String?,
      notes: freezed == notes
          ? _value.notes
          : notes // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$DistributionImpl implements _Distribution {
  _$DistributionImpl(
      {this.warehouseId, this.quantity, this.deliveryDate, this.notes});

  factory _$DistributionImpl.fromJson(Map<String, dynamic> json) =>
      _$$DistributionImplFromJson(json);

  @override
  final String? warehouseId;
// ID склада
  @override
  final double? quantity;
// Количество (>= 0)
  @override
  final String? deliveryDate;
// Дата поставки (YYYY-MM-DD)
  @override
  final String? notes;

  @override
  String toString() {
    return 'Distribution(warehouseId: $warehouseId, quantity: $quantity, deliveryDate: $deliveryDate, notes: $notes)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DistributionImpl &&
            (identical(other.warehouseId, warehouseId) ||
                other.warehouseId == warehouseId) &&
            (identical(other.quantity, quantity) ||
                other.quantity == quantity) &&
            (identical(other.deliveryDate, deliveryDate) ||
                other.deliveryDate == deliveryDate) &&
            (identical(other.notes, notes) || other.notes == notes));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, warehouseId, quantity, deliveryDate, notes);

  /// Create a copy of Distribution
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DistributionImplCopyWith<_$DistributionImpl> get copyWith =>
      __$$DistributionImplCopyWithImpl<_$DistributionImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$DistributionImplToJson(
      this,
    );
  }
}

abstract class _Distribution implements Distribution {
  factory _Distribution(
      {final String? warehouseId,
      final double? quantity,
      final String? deliveryDate,
      final String? notes}) = _$DistributionImpl;

  factory _Distribution.fromJson(Map<String, dynamic> json) =
      _$DistributionImpl.fromJson;

  @override
  String? get warehouseId; // ID склада
  @override
  double? get quantity; // Количество (>= 0)
  @override
  String? get deliveryDate; // Дата поставки (YYYY-MM-DD)
  @override
  String? get notes;

  /// Create a copy of Distribution
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DistributionImplCopyWith<_$DistributionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

PaymentDetail _$PaymentDetailFromJson(Map<String, dynamic> json) {
  return _PaymentDetail.fromJson(json);
}

/// @nodoc
mixin _$PaymentDetail {
  @JsonKey(name: 'paymentDate')
  String? get paymentDate => throw _privateConstructorUsedError; // Дата оплаты
  double? get amount => throw _privateConstructorUsedError;

  /// Serializes this PaymentDetail to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PaymentDetail
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PaymentDetailCopyWith<PaymentDetail> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PaymentDetailCopyWith<$Res> {
  factory $PaymentDetailCopyWith(
          PaymentDetail value, $Res Function(PaymentDetail) then) =
      _$PaymentDetailCopyWithImpl<$Res, PaymentDetail>;
  @useResult
  $Res call(
      {@JsonKey(name: 'paymentDate') String? paymentDate, double? amount});
}

/// @nodoc
class _$PaymentDetailCopyWithImpl<$Res, $Val extends PaymentDetail>
    implements $PaymentDetailCopyWith<$Res> {
  _$PaymentDetailCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PaymentDetail
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? paymentDate = freezed,
    Object? amount = freezed,
  }) {
    return _then(_value.copyWith(
      paymentDate: freezed == paymentDate
          ? _value.paymentDate
          : paymentDate // ignore: cast_nullable_to_non_nullable
              as String?,
      amount: freezed == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as double?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PaymentDetailImplCopyWith<$Res>
    implements $PaymentDetailCopyWith<$Res> {
  factory _$$PaymentDetailImplCopyWith(
          _$PaymentDetailImpl value, $Res Function(_$PaymentDetailImpl) then) =
      __$$PaymentDetailImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'paymentDate') String? paymentDate, double? amount});
}

/// @nodoc
class __$$PaymentDetailImplCopyWithImpl<$Res>
    extends _$PaymentDetailCopyWithImpl<$Res, _$PaymentDetailImpl>
    implements _$$PaymentDetailImplCopyWith<$Res> {
  __$$PaymentDetailImplCopyWithImpl(
      _$PaymentDetailImpl _value, $Res Function(_$PaymentDetailImpl) _then)
      : super(_value, _then);

  /// Create a copy of PaymentDetail
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? paymentDate = freezed,
    Object? amount = freezed,
  }) {
    return _then(_$PaymentDetailImpl(
      paymentDate: freezed == paymentDate
          ? _value.paymentDate
          : paymentDate // ignore: cast_nullable_to_non_nullable
              as String?,
      amount: freezed == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as double?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$PaymentDetailImpl implements _PaymentDetail {
  _$PaymentDetailImpl(
      {@JsonKey(name: 'paymentDate') this.paymentDate, this.amount});

  factory _$PaymentDetailImpl.fromJson(Map<String, dynamic> json) =>
      _$$PaymentDetailImplFromJson(json);

  @override
  @JsonKey(name: 'paymentDate')
  final String? paymentDate;
// Дата оплаты
  @override
  final double? amount;

  @override
  String toString() {
    return 'PaymentDetail(paymentDate: $paymentDate, amount: $amount)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PaymentDetailImpl &&
            (identical(other.paymentDate, paymentDate) ||
                other.paymentDate == paymentDate) &&
            (identical(other.amount, amount) || other.amount == amount));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, paymentDate, amount);

  /// Create a copy of PaymentDetail
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PaymentDetailImplCopyWith<_$PaymentDetailImpl> get copyWith =>
      __$$PaymentDetailImplCopyWithImpl<_$PaymentDetailImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PaymentDetailImplToJson(
      this,
    );
  }
}

abstract class _PaymentDetail implements PaymentDetail {
  factory _PaymentDetail(
      {@JsonKey(name: 'paymentDate') final String? paymentDate,
      final double? amount}) = _$PaymentDetailImpl;

  factory _PaymentDetail.fromJson(Map<String, dynamic> json) =
      _$PaymentDetailImpl.fromJson;

  @override
  @JsonKey(name: 'paymentDate')
  String? get paymentDate; // Дата оплаты
  @override
  double? get amount;

  /// Create a copy of PaymentDetail
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PaymentDetailImplCopyWith<_$PaymentDetailImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ProvisionUpdateContractInput _$ProvisionUpdateContractInputFromJson(
    Map<String, dynamic> json) {
  return _ProvisionUpdateContractInput.fromJson(json);
}

/// @nodoc
mixin _$ProvisionUpdateContractInput {
  String get contractId =>
      throw _privateConstructorUsedError; // ID контракта для обновления
  List<String> get productIds =>
      throw _privateConstructorUsedError; // ID продуктов
  @JsonKey(name: 'contractDate')
  String get contractDate =>
      throw _privateConstructorUsedError; // Дата заключения
  @JsonKey(name: 'contractStartDate')
  String? get contractStartDate =>
      throw _privateConstructorUsedError; // Дата начала
  @JsonKey(name: 'contractEndDate')
  String? get contractEndDate =>
      throw _privateConstructorUsedError; // Дата окончания
  String? get supplierId => throw _privateConstructorUsedError; // ID поставщика
  @JsonKey(name: 'plannedDeliveryDate')
  String? get plannedDeliveryDate =>
      throw _privateConstructorUsedError; // Плановая дата поставки
  double get contractPrice =>
      throw _privateConstructorUsedError; // Общая сумма контракта
  @JsonKey(name: 'productDetails')
  List<ProductDetail>? get productDetails =>
      throw _privateConstructorUsedError; // Детали продуктов
  @JsonKey(name: 'paymentDetails')
  List<PaymentDetail>? get paymentDetails =>
      throw _privateConstructorUsedError; // Детали оплат
  ProvisionsFilter? get filterType => throw _privateConstructorUsedError;
  String? get notes => throw _privateConstructorUsedError;

  /// Serializes this ProvisionUpdateContractInput to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProvisionUpdateContractInput
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProvisionUpdateContractInputCopyWith<ProvisionUpdateContractInput>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProvisionUpdateContractInputCopyWith<$Res> {
  factory $ProvisionUpdateContractInputCopyWith(
          ProvisionUpdateContractInput value,
          $Res Function(ProvisionUpdateContractInput) then) =
      _$ProvisionUpdateContractInputCopyWithImpl<$Res,
          ProvisionUpdateContractInput>;
  @useResult
  $Res call(
      {String contractId,
      List<String> productIds,
      @JsonKey(name: 'contractDate') String contractDate,
      @JsonKey(name: 'contractStartDate') String? contractStartDate,
      @JsonKey(name: 'contractEndDate') String? contractEndDate,
      String? supplierId,
      @JsonKey(name: 'plannedDeliveryDate') String? plannedDeliveryDate,
      double contractPrice,
      @JsonKey(name: 'productDetails') List<ProductDetail>? productDetails,
      @JsonKey(name: 'paymentDetails') List<PaymentDetail>? paymentDetails,
      ProvisionsFilter? filterType,
      String? notes});
}

/// @nodoc
class _$ProvisionUpdateContractInputCopyWithImpl<$Res,
        $Val extends ProvisionUpdateContractInput>
    implements $ProvisionUpdateContractInputCopyWith<$Res> {
  _$ProvisionUpdateContractInputCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProvisionUpdateContractInput
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? contractId = null,
    Object? productIds = null,
    Object? contractDate = null,
    Object? contractStartDate = freezed,
    Object? contractEndDate = freezed,
    Object? supplierId = freezed,
    Object? plannedDeliveryDate = freezed,
    Object? contractPrice = null,
    Object? productDetails = freezed,
    Object? paymentDetails = freezed,
    Object? filterType = freezed,
    Object? notes = freezed,
  }) {
    return _then(_value.copyWith(
      contractId: null == contractId
          ? _value.contractId
          : contractId // ignore: cast_nullable_to_non_nullable
              as String,
      productIds: null == productIds
          ? _value.productIds
          : productIds // ignore: cast_nullable_to_non_nullable
              as List<String>,
      contractDate: null == contractDate
          ? _value.contractDate
          : contractDate // ignore: cast_nullable_to_non_nullable
              as String,
      contractStartDate: freezed == contractStartDate
          ? _value.contractStartDate
          : contractStartDate // ignore: cast_nullable_to_non_nullable
              as String?,
      contractEndDate: freezed == contractEndDate
          ? _value.contractEndDate
          : contractEndDate // ignore: cast_nullable_to_non_nullable
              as String?,
      supplierId: freezed == supplierId
          ? _value.supplierId
          : supplierId // ignore: cast_nullable_to_non_nullable
              as String?,
      plannedDeliveryDate: freezed == plannedDeliveryDate
          ? _value.plannedDeliveryDate
          : plannedDeliveryDate // ignore: cast_nullable_to_non_nullable
              as String?,
      contractPrice: null == contractPrice
          ? _value.contractPrice
          : contractPrice // ignore: cast_nullable_to_non_nullable
              as double,
      productDetails: freezed == productDetails
          ? _value.productDetails
          : productDetails // ignore: cast_nullable_to_non_nullable
              as List<ProductDetail>?,
      paymentDetails: freezed == paymentDetails
          ? _value.paymentDetails
          : paymentDetails // ignore: cast_nullable_to_non_nullable
              as List<PaymentDetail>?,
      filterType: freezed == filterType
          ? _value.filterType
          : filterType // ignore: cast_nullable_to_non_nullable
              as ProvisionsFilter?,
      notes: freezed == notes
          ? _value.notes
          : notes // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ProvisionUpdateContractInputImplCopyWith<$Res>
    implements $ProvisionUpdateContractInputCopyWith<$Res> {
  factory _$$ProvisionUpdateContractInputImplCopyWith(
          _$ProvisionUpdateContractInputImpl value,
          $Res Function(_$ProvisionUpdateContractInputImpl) then) =
      __$$ProvisionUpdateContractInputImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String contractId,
      List<String> productIds,
      @JsonKey(name: 'contractDate') String contractDate,
      @JsonKey(name: 'contractStartDate') String? contractStartDate,
      @JsonKey(name: 'contractEndDate') String? contractEndDate,
      String? supplierId,
      @JsonKey(name: 'plannedDeliveryDate') String? plannedDeliveryDate,
      double contractPrice,
      @JsonKey(name: 'productDetails') List<ProductDetail>? productDetails,
      @JsonKey(name: 'paymentDetails') List<PaymentDetail>? paymentDetails,
      ProvisionsFilter? filterType,
      String? notes});
}

/// @nodoc
class __$$ProvisionUpdateContractInputImplCopyWithImpl<$Res>
    extends _$ProvisionUpdateContractInputCopyWithImpl<$Res,
        _$ProvisionUpdateContractInputImpl>
    implements _$$ProvisionUpdateContractInputImplCopyWith<$Res> {
  __$$ProvisionUpdateContractInputImplCopyWithImpl(
      _$ProvisionUpdateContractInputImpl _value,
      $Res Function(_$ProvisionUpdateContractInputImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProvisionUpdateContractInput
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? contractId = null,
    Object? productIds = null,
    Object? contractDate = null,
    Object? contractStartDate = freezed,
    Object? contractEndDate = freezed,
    Object? supplierId = freezed,
    Object? plannedDeliveryDate = freezed,
    Object? contractPrice = null,
    Object? productDetails = freezed,
    Object? paymentDetails = freezed,
    Object? filterType = freezed,
    Object? notes = freezed,
  }) {
    return _then(_$ProvisionUpdateContractInputImpl(
      contractId: null == contractId
          ? _value.contractId
          : contractId // ignore: cast_nullable_to_non_nullable
              as String,
      productIds: null == productIds
          ? _value._productIds
          : productIds // ignore: cast_nullable_to_non_nullable
              as List<String>,
      contractDate: null == contractDate
          ? _value.contractDate
          : contractDate // ignore: cast_nullable_to_non_nullable
              as String,
      contractStartDate: freezed == contractStartDate
          ? _value.contractStartDate
          : contractStartDate // ignore: cast_nullable_to_non_nullable
              as String?,
      contractEndDate: freezed == contractEndDate
          ? _value.contractEndDate
          : contractEndDate // ignore: cast_nullable_to_non_nullable
              as String?,
      supplierId: freezed == supplierId
          ? _value.supplierId
          : supplierId // ignore: cast_nullable_to_non_nullable
              as String?,
      plannedDeliveryDate: freezed == plannedDeliveryDate
          ? _value.plannedDeliveryDate
          : plannedDeliveryDate // ignore: cast_nullable_to_non_nullable
              as String?,
      contractPrice: null == contractPrice
          ? _value.contractPrice
          : contractPrice // ignore: cast_nullable_to_non_nullable
              as double,
      productDetails: freezed == productDetails
          ? _value._productDetails
          : productDetails // ignore: cast_nullable_to_non_nullable
              as List<ProductDetail>?,
      paymentDetails: freezed == paymentDetails
          ? _value._paymentDetails
          : paymentDetails // ignore: cast_nullable_to_non_nullable
              as List<PaymentDetail>?,
      filterType: freezed == filterType
          ? _value.filterType
          : filterType // ignore: cast_nullable_to_non_nullable
              as ProvisionsFilter?,
      notes: freezed == notes
          ? _value.notes
          : notes // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$ProvisionUpdateContractInputImpl
    implements _ProvisionUpdateContractInput {
  _$ProvisionUpdateContractInputImpl(
      {required this.contractId,
      required final List<String> productIds,
      @JsonKey(name: 'contractDate') required this.contractDate,
      @JsonKey(name: 'contractStartDate') this.contractStartDate,
      @JsonKey(name: 'contractEndDate') this.contractEndDate,
      this.supplierId,
      @JsonKey(name: 'plannedDeliveryDate') this.plannedDeliveryDate,
      required this.contractPrice,
      @JsonKey(name: 'productDetails')
      final List<ProductDetail>? productDetails,
      @JsonKey(name: 'paymentDetails')
      final List<PaymentDetail>? paymentDetails,
      this.filterType,
      this.notes})
      : _productIds = productIds,
        _productDetails = productDetails,
        _paymentDetails = paymentDetails;

  factory _$ProvisionUpdateContractInputImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$ProvisionUpdateContractInputImplFromJson(json);

  @override
  final String contractId;
// ID контракта для обновления
  final List<String> _productIds;
// ID контракта для обновления
  @override
  List<String> get productIds {
    if (_productIds is EqualUnmodifiableListView) return _productIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_productIds);
  }

// ID продуктов
  @override
  @JsonKey(name: 'contractDate')
  final String contractDate;
// Дата заключения
  @override
  @JsonKey(name: 'contractStartDate')
  final String? contractStartDate;
// Дата начала
  @override
  @JsonKey(name: 'contractEndDate')
  final String? contractEndDate;
// Дата окончания
  @override
  final String? supplierId;
// ID поставщика
  @override
  @JsonKey(name: 'plannedDeliveryDate')
  final String? plannedDeliveryDate;
// Плановая дата поставки
  @override
  final double contractPrice;
// Общая сумма контракта
  final List<ProductDetail>? _productDetails;
// Общая сумма контракта
  @override
  @JsonKey(name: 'productDetails')
  List<ProductDetail>? get productDetails {
    final value = _productDetails;
    if (value == null) return null;
    if (_productDetails is EqualUnmodifiableListView) return _productDetails;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

// Детали продуктов
  final List<PaymentDetail>? _paymentDetails;
// Детали продуктов
  @override
  @JsonKey(name: 'paymentDetails')
  List<PaymentDetail>? get paymentDetails {
    final value = _paymentDetails;
    if (value == null) return null;
    if (_paymentDetails is EqualUnmodifiableListView) return _paymentDetails;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

// Детали оплат
  @override
  final ProvisionsFilter? filterType;
  @override
  final String? notes;

  @override
  String toString() {
    return 'ProvisionUpdateContractInput(contractId: $contractId, productIds: $productIds, contractDate: $contractDate, contractStartDate: $contractStartDate, contractEndDate: $contractEndDate, supplierId: $supplierId, plannedDeliveryDate: $plannedDeliveryDate, contractPrice: $contractPrice, productDetails: $productDetails, paymentDetails: $paymentDetails, filterType: $filterType, notes: $notes)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProvisionUpdateContractInputImpl &&
            (identical(other.contractId, contractId) ||
                other.contractId == contractId) &&
            const DeepCollectionEquality()
                .equals(other._productIds, _productIds) &&
            (identical(other.contractDate, contractDate) ||
                other.contractDate == contractDate) &&
            (identical(other.contractStartDate, contractStartDate) ||
                other.contractStartDate == contractStartDate) &&
            (identical(other.contractEndDate, contractEndDate) ||
                other.contractEndDate == contractEndDate) &&
            (identical(other.supplierId, supplierId) ||
                other.supplierId == supplierId) &&
            (identical(other.plannedDeliveryDate, plannedDeliveryDate) ||
                other.plannedDeliveryDate == plannedDeliveryDate) &&
            (identical(other.contractPrice, contractPrice) ||
                other.contractPrice == contractPrice) &&
            const DeepCollectionEquality()
                .equals(other._productDetails, _productDetails) &&
            const DeepCollectionEquality()
                .equals(other._paymentDetails, _paymentDetails) &&
            (identical(other.filterType, filterType) ||
                other.filterType == filterType) &&
            (identical(other.notes, notes) || other.notes == notes));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      contractId,
      const DeepCollectionEquality().hash(_productIds),
      contractDate,
      contractStartDate,
      contractEndDate,
      supplierId,
      plannedDeliveryDate,
      contractPrice,
      const DeepCollectionEquality().hash(_productDetails),
      const DeepCollectionEquality().hash(_paymentDetails),
      filterType,
      notes);

  /// Create a copy of ProvisionUpdateContractInput
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProvisionUpdateContractInputImplCopyWith<
          _$ProvisionUpdateContractInputImpl>
      get copyWith => __$$ProvisionUpdateContractInputImplCopyWithImpl<
          _$ProvisionUpdateContractInputImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProvisionUpdateContractInputImplToJson(
      this,
    );
  }
}

abstract class _ProvisionUpdateContractInput
    implements ProvisionUpdateContractInput {
  factory _ProvisionUpdateContractInput(
      {required final String contractId,
      required final List<String> productIds,
      @JsonKey(name: 'contractDate') required final String contractDate,
      @JsonKey(name: 'contractStartDate') final String? contractStartDate,
      @JsonKey(name: 'contractEndDate') final String? contractEndDate,
      final String? supplierId,
      @JsonKey(name: 'plannedDeliveryDate') final String? plannedDeliveryDate,
      required final double contractPrice,
      @JsonKey(name: 'productDetails')
      final List<ProductDetail>? productDetails,
      @JsonKey(name: 'paymentDetails')
      final List<PaymentDetail>? paymentDetails,
      final ProvisionsFilter? filterType,
      final String? notes}) = _$ProvisionUpdateContractInputImpl;

  factory _ProvisionUpdateContractInput.fromJson(Map<String, dynamic> json) =
      _$ProvisionUpdateContractInputImpl.fromJson;

  @override
  String get contractId; // ID контракта для обновления
  @override
  List<String> get productIds; // ID продуктов
  @override
  @JsonKey(name: 'contractDate')
  String get contractDate; // Дата заключения
  @override
  @JsonKey(name: 'contractStartDate')
  String? get contractStartDate; // Дата начала
  @override
  @JsonKey(name: 'contractEndDate')
  String? get contractEndDate; // Дата окончания
  @override
  String? get supplierId; // ID поставщика
  @override
  @JsonKey(name: 'plannedDeliveryDate')
  String? get plannedDeliveryDate; // Плановая дата поставки
  @override
  double get contractPrice; // Общая сумма контракта
  @override
  @JsonKey(name: 'productDetails')
  List<ProductDetail>? get productDetails; // Детали продуктов
  @override
  @JsonKey(name: 'paymentDetails')
  List<PaymentDetail>? get paymentDetails; // Детали оплат
  @override
  ProvisionsFilter? get filterType;
  @override
  String? get notes;

  /// Create a copy of ProvisionUpdateContractInput
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProvisionUpdateContractInputImplCopyWith<
          _$ProvisionUpdateContractInputImpl>
      get copyWith => throw _privateConstructorUsedError;
}

ProvisionChangeContractStatusInput _$ProvisionChangeContractStatusInputFromJson(
    Map<String, dynamic> json) {
  return _ProvisionChangeContractStatusInput.fromJson(json);
}

/// @nodoc
mixin _$ProvisionChangeContractStatusInput {
  String? get contractId => throw _privateConstructorUsedError;
  ContractAction? get action => throw _privateConstructorUsedError;
  String? get reason => throw _privateConstructorUsedError;

  /// Serializes this ProvisionChangeContractStatusInput to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProvisionChangeContractStatusInput
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProvisionChangeContractStatusInputCopyWith<
          ProvisionChangeContractStatusInput>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProvisionChangeContractStatusInputCopyWith<$Res> {
  factory $ProvisionChangeContractStatusInputCopyWith(
          ProvisionChangeContractStatusInput value,
          $Res Function(ProvisionChangeContractStatusInput) then) =
      _$ProvisionChangeContractStatusInputCopyWithImpl<$Res,
          ProvisionChangeContractStatusInput>;
  @useResult
  $Res call({String? contractId, ContractAction? action, String? reason});
}

/// @nodoc
class _$ProvisionChangeContractStatusInputCopyWithImpl<$Res,
        $Val extends ProvisionChangeContractStatusInput>
    implements $ProvisionChangeContractStatusInputCopyWith<$Res> {
  _$ProvisionChangeContractStatusInputCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProvisionChangeContractStatusInput
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? contractId = freezed,
    Object? action = freezed,
    Object? reason = freezed,
  }) {
    return _then(_value.copyWith(
      contractId: freezed == contractId
          ? _value.contractId
          : contractId // ignore: cast_nullable_to_non_nullable
              as String?,
      action: freezed == action
          ? _value.action
          : action // ignore: cast_nullable_to_non_nullable
              as ContractAction?,
      reason: freezed == reason
          ? _value.reason
          : reason // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ProvisionChangeContractStatusInputImplCopyWith<$Res>
    implements $ProvisionChangeContractStatusInputCopyWith<$Res> {
  factory _$$ProvisionChangeContractStatusInputImplCopyWith(
          _$ProvisionChangeContractStatusInputImpl value,
          $Res Function(_$ProvisionChangeContractStatusInputImpl) then) =
      __$$ProvisionChangeContractStatusInputImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? contractId, ContractAction? action, String? reason});
}

/// @nodoc
class __$$ProvisionChangeContractStatusInputImplCopyWithImpl<$Res>
    extends _$ProvisionChangeContractStatusInputCopyWithImpl<$Res,
        _$ProvisionChangeContractStatusInputImpl>
    implements _$$ProvisionChangeContractStatusInputImplCopyWith<$Res> {
  __$$ProvisionChangeContractStatusInputImplCopyWithImpl(
      _$ProvisionChangeContractStatusInputImpl _value,
      $Res Function(_$ProvisionChangeContractStatusInputImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProvisionChangeContractStatusInput
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? contractId = freezed,
    Object? action = freezed,
    Object? reason = freezed,
  }) {
    return _then(_$ProvisionChangeContractStatusInputImpl(
      contractId: freezed == contractId
          ? _value.contractId
          : contractId // ignore: cast_nullable_to_non_nullable
              as String?,
      action: freezed == action
          ? _value.action
          : action // ignore: cast_nullable_to_non_nullable
              as ContractAction?,
      reason: freezed == reason
          ? _value.reason
          : reason // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$ProvisionChangeContractStatusInputImpl
    implements _ProvisionChangeContractStatusInput {
  _$ProvisionChangeContractStatusInputImpl(
      {this.contractId, this.action, this.reason});

  factory _$ProvisionChangeContractStatusInputImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$ProvisionChangeContractStatusInputImplFromJson(json);

  @override
  final String? contractId;
  @override
  final ContractAction? action;
  @override
  final String? reason;

  @override
  String toString() {
    return 'ProvisionChangeContractStatusInput(contractId: $contractId, action: $action, reason: $reason)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProvisionChangeContractStatusInputImpl &&
            (identical(other.contractId, contractId) ||
                other.contractId == contractId) &&
            (identical(other.action, action) || other.action == action) &&
            (identical(other.reason, reason) || other.reason == reason));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, contractId, action, reason);

  /// Create a copy of ProvisionChangeContractStatusInput
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProvisionChangeContractStatusInputImplCopyWith<
          _$ProvisionChangeContractStatusInputImpl>
      get copyWith => __$$ProvisionChangeContractStatusInputImplCopyWithImpl<
          _$ProvisionChangeContractStatusInputImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProvisionChangeContractStatusInputImplToJson(
      this,
    );
  }
}

abstract class _ProvisionChangeContractStatusInput
    implements ProvisionChangeContractStatusInput {
  factory _ProvisionChangeContractStatusInput(
      {final String? contractId,
      final ContractAction? action,
      final String? reason}) = _$ProvisionChangeContractStatusInputImpl;

  factory _ProvisionChangeContractStatusInput.fromJson(
          Map<String, dynamic> json) =
      _$ProvisionChangeContractStatusInputImpl.fromJson;

  @override
  String? get contractId;
  @override
  ContractAction? get action;
  @override
  String? get reason;

  /// Create a copy of ProvisionChangeContractStatusInput
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProvisionChangeContractStatusInputImplCopyWith<
          _$ProvisionChangeContractStatusInputImpl>
      get copyWith => throw _privateConstructorUsedError;
}

Warehouse _$WarehouseFromJson(Map<String, dynamic> json) {
  return _Warehouse.fromJson(json);
}

/// @nodoc
mixin _$Warehouse {
  @JsonKey(name: '_id')
  String? get id => throw _privateConstructorUsedError; // ID склада
  String get name => throw _privateConstructorUsedError; // Название склада
  @JsonKey(name: 'type')
  WarehouseType get type => throw _privateConstructorUsedError; // Тип склада
  String? get address =>
      throw _privateConstructorUsedError; // Адрес склада (для физических складов)
  String? get description =>
      throw _privateConstructorUsedError; // Описание склада
  String? get projectId =>
      throw _privateConstructorUsedError; // ID проекта (для виртуальных складов проекта)
  String? get physicalWarehouseId =>
      throw _privateConstructorUsedError; // ID физического склада (для виртуальных складов)
  String? get contractorId =>
      throw _privateConstructorUsedError; // ID контрагента (для складов контрагента)
  List<WarehouseKeeper>? get keepers =>
      throw _privateConstructorUsedError; // Кладовщики склада
  bool? get isActive => throw _privateConstructorUsedError; // Активен ли склад
  @JsonKey(name: 'createdAt')
  String? get createdAt => throw _privateConstructorUsedError; // Дата создания
  @JsonKey(name: 'updatedAt')
  String? get updatedAt =>
      throw _privateConstructorUsedError; // Дата обновления
  String? get createdBy =>
      throw _privateConstructorUsedError; // ID пользователя, создавшего склад
  String? get updatedBy => throw _privateConstructorUsedError;

  /// Serializes this Warehouse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Warehouse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $WarehouseCopyWith<Warehouse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WarehouseCopyWith<$Res> {
  factory $WarehouseCopyWith(Warehouse value, $Res Function(Warehouse) then) =
      _$WarehouseCopyWithImpl<$Res, Warehouse>;
  @useResult
  $Res call(
      {@JsonKey(name: '_id') String? id,
      String name,
      @JsonKey(name: 'type') WarehouseType type,
      String? address,
      String? description,
      String? projectId,
      String? physicalWarehouseId,
      String? contractorId,
      List<WarehouseKeeper>? keepers,
      bool? isActive,
      @JsonKey(name: 'createdAt') String? createdAt,
      @JsonKey(name: 'updatedAt') String? updatedAt,
      String? createdBy,
      String? updatedBy});
}

/// @nodoc
class _$WarehouseCopyWithImpl<$Res, $Val extends Warehouse>
    implements $WarehouseCopyWith<$Res> {
  _$WarehouseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Warehouse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? name = null,
    Object? type = null,
    Object? address = freezed,
    Object? description = freezed,
    Object? projectId = freezed,
    Object? physicalWarehouseId = freezed,
    Object? contractorId = freezed,
    Object? keepers = freezed,
    Object? isActive = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
    Object? createdBy = freezed,
    Object? updatedBy = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as WarehouseType,
      address: freezed == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      projectId: freezed == projectId
          ? _value.projectId
          : projectId // ignore: cast_nullable_to_non_nullable
              as String?,
      physicalWarehouseId: freezed == physicalWarehouseId
          ? _value.physicalWarehouseId
          : physicalWarehouseId // ignore: cast_nullable_to_non_nullable
              as String?,
      contractorId: freezed == contractorId
          ? _value.contractorId
          : contractorId // ignore: cast_nullable_to_non_nullable
              as String?,
      keepers: freezed == keepers
          ? _value.keepers
          : keepers // ignore: cast_nullable_to_non_nullable
              as List<WarehouseKeeper>?,
      isActive: freezed == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as String?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as String?,
      createdBy: freezed == createdBy
          ? _value.createdBy
          : createdBy // ignore: cast_nullable_to_non_nullable
              as String?,
      updatedBy: freezed == updatedBy
          ? _value.updatedBy
          : updatedBy // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$WarehouseImplCopyWith<$Res>
    implements $WarehouseCopyWith<$Res> {
  factory _$$WarehouseImplCopyWith(
          _$WarehouseImpl value, $Res Function(_$WarehouseImpl) then) =
      __$$WarehouseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: '_id') String? id,
      String name,
      @JsonKey(name: 'type') WarehouseType type,
      String? address,
      String? description,
      String? projectId,
      String? physicalWarehouseId,
      String? contractorId,
      List<WarehouseKeeper>? keepers,
      bool? isActive,
      @JsonKey(name: 'createdAt') String? createdAt,
      @JsonKey(name: 'updatedAt') String? updatedAt,
      String? createdBy,
      String? updatedBy});
}

/// @nodoc
class __$$WarehouseImplCopyWithImpl<$Res>
    extends _$WarehouseCopyWithImpl<$Res, _$WarehouseImpl>
    implements _$$WarehouseImplCopyWith<$Res> {
  __$$WarehouseImplCopyWithImpl(
      _$WarehouseImpl _value, $Res Function(_$WarehouseImpl) _then)
      : super(_value, _then);

  /// Create a copy of Warehouse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? name = null,
    Object? type = null,
    Object? address = freezed,
    Object? description = freezed,
    Object? projectId = freezed,
    Object? physicalWarehouseId = freezed,
    Object? contractorId = freezed,
    Object? keepers = freezed,
    Object? isActive = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
    Object? createdBy = freezed,
    Object? updatedBy = freezed,
  }) {
    return _then(_$WarehouseImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as WarehouseType,
      address: freezed == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      projectId: freezed == projectId
          ? _value.projectId
          : projectId // ignore: cast_nullable_to_non_nullable
              as String?,
      physicalWarehouseId: freezed == physicalWarehouseId
          ? _value.physicalWarehouseId
          : physicalWarehouseId // ignore: cast_nullable_to_non_nullable
              as String?,
      contractorId: freezed == contractorId
          ? _value.contractorId
          : contractorId // ignore: cast_nullable_to_non_nullable
              as String?,
      keepers: freezed == keepers
          ? _value._keepers
          : keepers // ignore: cast_nullable_to_non_nullable
              as List<WarehouseKeeper>?,
      isActive: freezed == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as String?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as String?,
      createdBy: freezed == createdBy
          ? _value.createdBy
          : createdBy // ignore: cast_nullable_to_non_nullable
              as String?,
      updatedBy: freezed == updatedBy
          ? _value.updatedBy
          : updatedBy // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$WarehouseImpl implements _Warehouse {
  _$WarehouseImpl(
      {@JsonKey(name: '_id') this.id,
      required this.name,
      @JsonKey(name: 'type') required this.type,
      this.address,
      this.description,
      this.projectId,
      this.physicalWarehouseId,
      this.contractorId,
      final List<WarehouseKeeper>? keepers,
      this.isActive,
      @JsonKey(name: 'createdAt') this.createdAt,
      @JsonKey(name: 'updatedAt') this.updatedAt,
      this.createdBy,
      this.updatedBy})
      : _keepers = keepers;

  factory _$WarehouseImpl.fromJson(Map<String, dynamic> json) =>
      _$$WarehouseImplFromJson(json);

  @override
  @JsonKey(name: '_id')
  final String? id;
// ID склада
  @override
  final String name;
// Название склада
  @override
  @JsonKey(name: 'type')
  final WarehouseType type;
// Тип склада
  @override
  final String? address;
// Адрес склада (для физических складов)
  @override
  final String? description;
// Описание склада
  @override
  final String? projectId;
// ID проекта (для виртуальных складов проекта)
  @override
  final String? physicalWarehouseId;
// ID физического склада (для виртуальных складов)
  @override
  final String? contractorId;
// ID контрагента (для складов контрагента)
  final List<WarehouseKeeper>? _keepers;
// ID контрагента (для складов контрагента)
  @override
  List<WarehouseKeeper>? get keepers {
    final value = _keepers;
    if (value == null) return null;
    if (_keepers is EqualUnmodifiableListView) return _keepers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

// Кладовщики склада
  @override
  final bool? isActive;
// Активен ли склад
  @override
  @JsonKey(name: 'createdAt')
  final String? createdAt;
// Дата создания
  @override
  @JsonKey(name: 'updatedAt')
  final String? updatedAt;
// Дата обновления
  @override
  final String? createdBy;
// ID пользователя, создавшего склад
  @override
  final String? updatedBy;

  @override
  String toString() {
    return 'Warehouse(id: $id, name: $name, type: $type, address: $address, description: $description, projectId: $projectId, physicalWarehouseId: $physicalWarehouseId, contractorId: $contractorId, keepers: $keepers, isActive: $isActive, createdAt: $createdAt, updatedAt: $updatedAt, createdBy: $createdBy, updatedBy: $updatedBy)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WarehouseImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.address, address) || other.address == address) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.projectId, projectId) ||
                other.projectId == projectId) &&
            (identical(other.physicalWarehouseId, physicalWarehouseId) ||
                other.physicalWarehouseId == physicalWarehouseId) &&
            (identical(other.contractorId, contractorId) ||
                other.contractorId == contractorId) &&
            const DeepCollectionEquality().equals(other._keepers, _keepers) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.createdBy, createdBy) ||
                other.createdBy == createdBy) &&
            (identical(other.updatedBy, updatedBy) ||
                other.updatedBy == updatedBy));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      name,
      type,
      address,
      description,
      projectId,
      physicalWarehouseId,
      contractorId,
      const DeepCollectionEquality().hash(_keepers),
      isActive,
      createdAt,
      updatedAt,
      createdBy,
      updatedBy);

  /// Create a copy of Warehouse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$WarehouseImplCopyWith<_$WarehouseImpl> get copyWith =>
      __$$WarehouseImplCopyWithImpl<_$WarehouseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$WarehouseImplToJson(
      this,
    );
  }
}

abstract class _Warehouse implements Warehouse {
  factory _Warehouse(
      {@JsonKey(name: '_id') final String? id,
      required final String name,
      @JsonKey(name: 'type') required final WarehouseType type,
      final String? address,
      final String? description,
      final String? projectId,
      final String? physicalWarehouseId,
      final String? contractorId,
      final List<WarehouseKeeper>? keepers,
      final bool? isActive,
      @JsonKey(name: 'createdAt') final String? createdAt,
      @JsonKey(name: 'updatedAt') final String? updatedAt,
      final String? createdBy,
      final String? updatedBy}) = _$WarehouseImpl;

  factory _Warehouse.fromJson(Map<String, dynamic> json) =
      _$WarehouseImpl.fromJson;

  @override
  @JsonKey(name: '_id')
  String? get id; // ID склада
  @override
  String get name; // Название склада
  @override
  @JsonKey(name: 'type')
  WarehouseType get type; // Тип склада
  @override
  String? get address; // Адрес склада (для физических складов)
  @override
  String? get description; // Описание склада
  @override
  String? get projectId; // ID проекта (для виртуальных складов проекта)
  @override
  String?
      get physicalWarehouseId; // ID физического склада (для виртуальных складов)
  @override
  String? get contractorId; // ID контрагента (для складов контрагента)
  @override
  List<WarehouseKeeper>? get keepers; // Кладовщики склада
  @override
  bool? get isActive; // Активен ли склад
  @override
  @JsonKey(name: 'createdAt')
  String? get createdAt; // Дата создания
  @override
  @JsonKey(name: 'updatedAt')
  String? get updatedAt; // Дата обновления
  @override
  String? get createdBy; // ID пользователя, создавшего склад
  @override
  String? get updatedBy;

  /// Create a copy of Warehouse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$WarehouseImplCopyWith<_$WarehouseImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

WarehouseKeeper _$WarehouseKeeperFromJson(Map<String, dynamic> json) {
  return _WarehouseKeeper.fromJson(json);
}

/// @nodoc
mixin _$WarehouseKeeper {
  String? get id => throw _privateConstructorUsedError; // ID кладовщика
  String? get name => throw _privateConstructorUsedError; // Имя кладовщика
  String? get email => throw _privateConstructorUsedError; // Email кладовщика
  bool? get isActive => throw _privateConstructorUsedError;

  /// Serializes this WarehouseKeeper to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of WarehouseKeeper
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $WarehouseKeeperCopyWith<WarehouseKeeper> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WarehouseKeeperCopyWith<$Res> {
  factory $WarehouseKeeperCopyWith(
          WarehouseKeeper value, $Res Function(WarehouseKeeper) then) =
      _$WarehouseKeeperCopyWithImpl<$Res, WarehouseKeeper>;
  @useResult
  $Res call({String? id, String? name, String? email, bool? isActive});
}

/// @nodoc
class _$WarehouseKeeperCopyWithImpl<$Res, $Val extends WarehouseKeeper>
    implements $WarehouseKeeperCopyWith<$Res> {
  _$WarehouseKeeperCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of WarehouseKeeper
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? name = freezed,
    Object? email = freezed,
    Object? isActive = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      isActive: freezed == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$WarehouseKeeperImplCopyWith<$Res>
    implements $WarehouseKeeperCopyWith<$Res> {
  factory _$$WarehouseKeeperImplCopyWith(_$WarehouseKeeperImpl value,
          $Res Function(_$WarehouseKeeperImpl) then) =
      __$$WarehouseKeeperImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? id, String? name, String? email, bool? isActive});
}

/// @nodoc
class __$$WarehouseKeeperImplCopyWithImpl<$Res>
    extends _$WarehouseKeeperCopyWithImpl<$Res, _$WarehouseKeeperImpl>
    implements _$$WarehouseKeeperImplCopyWith<$Res> {
  __$$WarehouseKeeperImplCopyWithImpl(
      _$WarehouseKeeperImpl _value, $Res Function(_$WarehouseKeeperImpl) _then)
      : super(_value, _then);

  /// Create a copy of WarehouseKeeper
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? name = freezed,
    Object? email = freezed,
    Object? isActive = freezed,
  }) {
    return _then(_$WarehouseKeeperImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      isActive: freezed == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$WarehouseKeeperImpl implements _WarehouseKeeper {
  _$WarehouseKeeperImpl({this.id, this.name, this.email, this.isActive});

  factory _$WarehouseKeeperImpl.fromJson(Map<String, dynamic> json) =>
      _$$WarehouseKeeperImplFromJson(json);

  @override
  final String? id;
// ID кладовщика
  @override
  final String? name;
// Имя кладовщика
  @override
  final String? email;
// Email кладовщика
  @override
  final bool? isActive;

  @override
  String toString() {
    return 'WarehouseKeeper(id: $id, name: $name, email: $email, isActive: $isActive)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WarehouseKeeperImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, name, email, isActive);

  /// Create a copy of WarehouseKeeper
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$WarehouseKeeperImplCopyWith<_$WarehouseKeeperImpl> get copyWith =>
      __$$WarehouseKeeperImplCopyWithImpl<_$WarehouseKeeperImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$WarehouseKeeperImplToJson(
      this,
    );
  }
}

abstract class _WarehouseKeeper implements WarehouseKeeper {
  factory _WarehouseKeeper(
      {final String? id,
      final String? name,
      final String? email,
      final bool? isActive}) = _$WarehouseKeeperImpl;

  factory _WarehouseKeeper.fromJson(Map<String, dynamic> json) =
      _$WarehouseKeeperImpl.fromJson;

  @override
  String? get id; // ID кладовщика
  @override
  String? get name; // Имя кладовщика
  @override
  String? get email; // Email кладовщика
  @override
  bool? get isActive;

  /// Create a copy of WarehouseKeeper
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$WarehouseKeeperImplCopyWith<_$WarehouseKeeperImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

WarehouseCreateInput _$WarehouseCreateInputFromJson(Map<String, dynamic> json) {
  return _WarehouseCreateInput.fromJson(json);
}

/// @nodoc
mixin _$WarehouseCreateInput {
  String get name => throw _privateConstructorUsedError; // Название склада
  @JsonKey(name: 'type')
  WarehouseType get type => throw _privateConstructorUsedError; // Тип склада
  String? get address => throw _privateConstructorUsedError; // Адрес склада
  String? get description => throw _privateConstructorUsedError;

  /// Serializes this WarehouseCreateInput to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of WarehouseCreateInput
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $WarehouseCreateInputCopyWith<WarehouseCreateInput> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WarehouseCreateInputCopyWith<$Res> {
  factory $WarehouseCreateInputCopyWith(WarehouseCreateInput value,
          $Res Function(WarehouseCreateInput) then) =
      _$WarehouseCreateInputCopyWithImpl<$Res, WarehouseCreateInput>;
  @useResult
  $Res call(
      {String name,
      @JsonKey(name: 'type') WarehouseType type,
      String? address,
      String? description});
}

/// @nodoc
class _$WarehouseCreateInputCopyWithImpl<$Res,
        $Val extends WarehouseCreateInput>
    implements $WarehouseCreateInputCopyWith<$Res> {
  _$WarehouseCreateInputCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of WarehouseCreateInput
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? type = null,
    Object? address = freezed,
    Object? description = freezed,
  }) {
    return _then(_value.copyWith(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as WarehouseType,
      address: freezed == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$WarehouseCreateInputImplCopyWith<$Res>
    implements $WarehouseCreateInputCopyWith<$Res> {
  factory _$$WarehouseCreateInputImplCopyWith(_$WarehouseCreateInputImpl value,
          $Res Function(_$WarehouseCreateInputImpl) then) =
      __$$WarehouseCreateInputImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String name,
      @JsonKey(name: 'type') WarehouseType type,
      String? address,
      String? description});
}

/// @nodoc
class __$$WarehouseCreateInputImplCopyWithImpl<$Res>
    extends _$WarehouseCreateInputCopyWithImpl<$Res, _$WarehouseCreateInputImpl>
    implements _$$WarehouseCreateInputImplCopyWith<$Res> {
  __$$WarehouseCreateInputImplCopyWithImpl(_$WarehouseCreateInputImpl _value,
      $Res Function(_$WarehouseCreateInputImpl) _then)
      : super(_value, _then);

  /// Create a copy of WarehouseCreateInput
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? type = null,
    Object? address = freezed,
    Object? description = freezed,
  }) {
    return _then(_$WarehouseCreateInputImpl(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as WarehouseType,
      address: freezed == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$WarehouseCreateInputImpl implements _WarehouseCreateInput {
  _$WarehouseCreateInputImpl(
      {required this.name,
      @JsonKey(name: 'type') required this.type,
      this.address,
      this.description});

  factory _$WarehouseCreateInputImpl.fromJson(Map<String, dynamic> json) =>
      _$$WarehouseCreateInputImplFromJson(json);

  @override
  final String name;
// Название склада
  @override
  @JsonKey(name: 'type')
  final WarehouseType type;
// Тип склада
  @override
  final String? address;
// Адрес склада
  @override
  final String? description;

  @override
  String toString() {
    return 'WarehouseCreateInput(name: $name, type: $type, address: $address, description: $description)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WarehouseCreateInputImpl &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.address, address) || other.address == address) &&
            (identical(other.description, description) ||
                other.description == description));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, name, type, address, description);

  /// Create a copy of WarehouseCreateInput
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$WarehouseCreateInputImplCopyWith<_$WarehouseCreateInputImpl>
      get copyWith =>
          __$$WarehouseCreateInputImplCopyWithImpl<_$WarehouseCreateInputImpl>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$WarehouseCreateInputImplToJson(
      this,
    );
  }
}

abstract class _WarehouseCreateInput implements WarehouseCreateInput {
  factory _WarehouseCreateInput(
      {required final String name,
      @JsonKey(name: 'type') required final WarehouseType type,
      final String? address,
      final String? description}) = _$WarehouseCreateInputImpl;

  factory _WarehouseCreateInput.fromJson(Map<String, dynamic> json) =
      _$WarehouseCreateInputImpl.fromJson;

  @override
  String get name; // Название склада
  @override
  @JsonKey(name: 'type')
  WarehouseType get type; // Тип склада
  @override
  String? get address; // Адрес склада
  @override
  String? get description;

  /// Create a copy of WarehouseCreateInput
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$WarehouseCreateInputImplCopyWith<_$WarehouseCreateInputImpl>
      get copyWith => throw _privateConstructorUsedError;
}

WarehouseUpdateInput _$WarehouseUpdateInputFromJson(Map<String, dynamic> json) {
  return _WarehouseUpdateInput.fromJson(json);
}

/// @nodoc
mixin _$WarehouseUpdateInput {
  String get warehouseId => throw _privateConstructorUsedError; // ID склада
  String? get name => throw _privateConstructorUsedError; // Название склада
  String? get address => throw _privateConstructorUsedError; // Адрес склада
  String? get description => throw _privateConstructorUsedError;

  /// Serializes this WarehouseUpdateInput to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of WarehouseUpdateInput
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $WarehouseUpdateInputCopyWith<WarehouseUpdateInput> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WarehouseUpdateInputCopyWith<$Res> {
  factory $WarehouseUpdateInputCopyWith(WarehouseUpdateInput value,
          $Res Function(WarehouseUpdateInput) then) =
      _$WarehouseUpdateInputCopyWithImpl<$Res, WarehouseUpdateInput>;
  @useResult
  $Res call(
      {String warehouseId, String? name, String? address, String? description});
}

/// @nodoc
class _$WarehouseUpdateInputCopyWithImpl<$Res,
        $Val extends WarehouseUpdateInput>
    implements $WarehouseUpdateInputCopyWith<$Res> {
  _$WarehouseUpdateInputCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of WarehouseUpdateInput
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? warehouseId = null,
    Object? name = freezed,
    Object? address = freezed,
    Object? description = freezed,
  }) {
    return _then(_value.copyWith(
      warehouseId: null == warehouseId
          ? _value.warehouseId
          : warehouseId // ignore: cast_nullable_to_non_nullable
              as String,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      address: freezed == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$WarehouseUpdateInputImplCopyWith<$Res>
    implements $WarehouseUpdateInputCopyWith<$Res> {
  factory _$$WarehouseUpdateInputImplCopyWith(_$WarehouseUpdateInputImpl value,
          $Res Function(_$WarehouseUpdateInputImpl) then) =
      __$$WarehouseUpdateInputImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String warehouseId, String? name, String? address, String? description});
}

/// @nodoc
class __$$WarehouseUpdateInputImplCopyWithImpl<$Res>
    extends _$WarehouseUpdateInputCopyWithImpl<$Res, _$WarehouseUpdateInputImpl>
    implements _$$WarehouseUpdateInputImplCopyWith<$Res> {
  __$$WarehouseUpdateInputImplCopyWithImpl(_$WarehouseUpdateInputImpl _value,
      $Res Function(_$WarehouseUpdateInputImpl) _then)
      : super(_value, _then);

  /// Create a copy of WarehouseUpdateInput
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? warehouseId = null,
    Object? name = freezed,
    Object? address = freezed,
    Object? description = freezed,
  }) {
    return _then(_$WarehouseUpdateInputImpl(
      warehouseId: null == warehouseId
          ? _value.warehouseId
          : warehouseId // ignore: cast_nullable_to_non_nullable
              as String,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      address: freezed == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$WarehouseUpdateInputImpl implements _WarehouseUpdateInput {
  _$WarehouseUpdateInputImpl(
      {required this.warehouseId, this.name, this.address, this.description});

  factory _$WarehouseUpdateInputImpl.fromJson(Map<String, dynamic> json) =>
      _$$WarehouseUpdateInputImplFromJson(json);

  @override
  final String warehouseId;
// ID склада
  @override
  final String? name;
// Название склада
  @override
  final String? address;
// Адрес склада
  @override
  final String? description;

  @override
  String toString() {
    return 'WarehouseUpdateInput(warehouseId: $warehouseId, name: $name, address: $address, description: $description)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WarehouseUpdateInputImpl &&
            (identical(other.warehouseId, warehouseId) ||
                other.warehouseId == warehouseId) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.address, address) || other.address == address) &&
            (identical(other.description, description) ||
                other.description == description));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, warehouseId, name, address, description);

  /// Create a copy of WarehouseUpdateInput
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$WarehouseUpdateInputImplCopyWith<_$WarehouseUpdateInputImpl>
      get copyWith =>
          __$$WarehouseUpdateInputImplCopyWithImpl<_$WarehouseUpdateInputImpl>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$WarehouseUpdateInputImplToJson(
      this,
    );
  }
}

abstract class _WarehouseUpdateInput implements WarehouseUpdateInput {
  factory _WarehouseUpdateInput(
      {required final String warehouseId,
      final String? name,
      final String? address,
      final String? description}) = _$WarehouseUpdateInputImpl;

  factory _WarehouseUpdateInput.fromJson(Map<String, dynamic> json) =
      _$WarehouseUpdateInputImpl.fromJson;

  @override
  String get warehouseId; // ID склада
  @override
  String? get name; // Название склада
  @override
  String? get address; // Адрес склада
  @override
  String? get description;

  /// Create a copy of WarehouseUpdateInput
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$WarehouseUpdateInputImplCopyWith<_$WarehouseUpdateInputImpl>
      get copyWith => throw _privateConstructorUsedError;
}

WarehouseDeleteInput _$WarehouseDeleteInputFromJson(Map<String, dynamic> json) {
  return _WarehouseDeleteInput.fromJson(json);
}

/// @nodoc
mixin _$WarehouseDeleteInput {
  String get warehouseId => throw _privateConstructorUsedError;

  /// Serializes this WarehouseDeleteInput to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of WarehouseDeleteInput
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $WarehouseDeleteInputCopyWith<WarehouseDeleteInput> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WarehouseDeleteInputCopyWith<$Res> {
  factory $WarehouseDeleteInputCopyWith(WarehouseDeleteInput value,
          $Res Function(WarehouseDeleteInput) then) =
      _$WarehouseDeleteInputCopyWithImpl<$Res, WarehouseDeleteInput>;
  @useResult
  $Res call({String warehouseId});
}

/// @nodoc
class _$WarehouseDeleteInputCopyWithImpl<$Res,
        $Val extends WarehouseDeleteInput>
    implements $WarehouseDeleteInputCopyWith<$Res> {
  _$WarehouseDeleteInputCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of WarehouseDeleteInput
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? warehouseId = null,
  }) {
    return _then(_value.copyWith(
      warehouseId: null == warehouseId
          ? _value.warehouseId
          : warehouseId // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$WarehouseDeleteInputImplCopyWith<$Res>
    implements $WarehouseDeleteInputCopyWith<$Res> {
  factory _$$WarehouseDeleteInputImplCopyWith(_$WarehouseDeleteInputImpl value,
          $Res Function(_$WarehouseDeleteInputImpl) then) =
      __$$WarehouseDeleteInputImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String warehouseId});
}

/// @nodoc
class __$$WarehouseDeleteInputImplCopyWithImpl<$Res>
    extends _$WarehouseDeleteInputCopyWithImpl<$Res, _$WarehouseDeleteInputImpl>
    implements _$$WarehouseDeleteInputImplCopyWith<$Res> {
  __$$WarehouseDeleteInputImplCopyWithImpl(_$WarehouseDeleteInputImpl _value,
      $Res Function(_$WarehouseDeleteInputImpl) _then)
      : super(_value, _then);

  /// Create a copy of WarehouseDeleteInput
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? warehouseId = null,
  }) {
    return _then(_$WarehouseDeleteInputImpl(
      warehouseId: null == warehouseId
          ? _value.warehouseId
          : warehouseId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$WarehouseDeleteInputImpl implements _WarehouseDeleteInput {
  _$WarehouseDeleteInputImpl({required this.warehouseId});

  factory _$WarehouseDeleteInputImpl.fromJson(Map<String, dynamic> json) =>
      _$$WarehouseDeleteInputImplFromJson(json);

  @override
  final String warehouseId;

  @override
  String toString() {
    return 'WarehouseDeleteInput(warehouseId: $warehouseId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WarehouseDeleteInputImpl &&
            (identical(other.warehouseId, warehouseId) ||
                other.warehouseId == warehouseId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, warehouseId);

  /// Create a copy of WarehouseDeleteInput
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$WarehouseDeleteInputImplCopyWith<_$WarehouseDeleteInputImpl>
      get copyWith =>
          __$$WarehouseDeleteInputImplCopyWithImpl<_$WarehouseDeleteInputImpl>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$WarehouseDeleteInputImplToJson(
      this,
    );
  }
}

abstract class _WarehouseDeleteInput implements WarehouseDeleteInput {
  factory _WarehouseDeleteInput({required final String warehouseId}) =
      _$WarehouseDeleteInputImpl;

  factory _WarehouseDeleteInput.fromJson(Map<String, dynamic> json) =
      _$WarehouseDeleteInputImpl.fromJson;

  @override
  String get warehouseId;

  /// Create a copy of WarehouseDeleteInput
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$WarehouseDeleteInputImplCopyWith<_$WarehouseDeleteInputImpl>
      get copyWith => throw _privateConstructorUsedError;
}

WarehouseViewInput _$WarehouseViewInputFromJson(Map<String, dynamic> json) {
  return _WarehouseViewInput.fromJson(json);
}

/// @nodoc
mixin _$WarehouseViewInput {
  String get warehouseId => throw _privateConstructorUsedError;

  /// Serializes this WarehouseViewInput to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of WarehouseViewInput
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $WarehouseViewInputCopyWith<WarehouseViewInput> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WarehouseViewInputCopyWith<$Res> {
  factory $WarehouseViewInputCopyWith(
          WarehouseViewInput value, $Res Function(WarehouseViewInput) then) =
      _$WarehouseViewInputCopyWithImpl<$Res, WarehouseViewInput>;
  @useResult
  $Res call({String warehouseId});
}

/// @nodoc
class _$WarehouseViewInputCopyWithImpl<$Res, $Val extends WarehouseViewInput>
    implements $WarehouseViewInputCopyWith<$Res> {
  _$WarehouseViewInputCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of WarehouseViewInput
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? warehouseId = null,
  }) {
    return _then(_value.copyWith(
      warehouseId: null == warehouseId
          ? _value.warehouseId
          : warehouseId // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$WarehouseViewInputImplCopyWith<$Res>
    implements $WarehouseViewInputCopyWith<$Res> {
  factory _$$WarehouseViewInputImplCopyWith(_$WarehouseViewInputImpl value,
          $Res Function(_$WarehouseViewInputImpl) then) =
      __$$WarehouseViewInputImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String warehouseId});
}

/// @nodoc
class __$$WarehouseViewInputImplCopyWithImpl<$Res>
    extends _$WarehouseViewInputCopyWithImpl<$Res, _$WarehouseViewInputImpl>
    implements _$$WarehouseViewInputImplCopyWith<$Res> {
  __$$WarehouseViewInputImplCopyWithImpl(_$WarehouseViewInputImpl _value,
      $Res Function(_$WarehouseViewInputImpl) _then)
      : super(_value, _then);

  /// Create a copy of WarehouseViewInput
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? warehouseId = null,
  }) {
    return _then(_$WarehouseViewInputImpl(
      warehouseId: null == warehouseId
          ? _value.warehouseId
          : warehouseId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$WarehouseViewInputImpl implements _WarehouseViewInput {
  _$WarehouseViewInputImpl({required this.warehouseId});

  factory _$WarehouseViewInputImpl.fromJson(Map<String, dynamic> json) =>
      _$$WarehouseViewInputImplFromJson(json);

  @override
  final String warehouseId;

  @override
  String toString() {
    return 'WarehouseViewInput(warehouseId: $warehouseId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WarehouseViewInputImpl &&
            (identical(other.warehouseId, warehouseId) ||
                other.warehouseId == warehouseId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, warehouseId);

  /// Create a copy of WarehouseViewInput
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$WarehouseViewInputImplCopyWith<_$WarehouseViewInputImpl> get copyWith =>
      __$$WarehouseViewInputImplCopyWithImpl<_$WarehouseViewInputImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$WarehouseViewInputImplToJson(
      this,
    );
  }
}

abstract class _WarehouseViewInput implements WarehouseViewInput {
  factory _WarehouseViewInput({required final String warehouseId}) =
      _$WarehouseViewInputImpl;

  factory _WarehouseViewInput.fromJson(Map<String, dynamic> json) =
      _$WarehouseViewInputImpl.fromJson;

  @override
  String get warehouseId;

  /// Create a copy of WarehouseViewInput
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$WarehouseViewInputImplCopyWith<_$WarehouseViewInputImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

WarehouseSearchInput _$WarehouseSearchInputFromJson(Map<String, dynamic> json) {
  return _WarehouseSearchInput.fromJson(json);
}

/// @nodoc
mixin _$WarehouseSearchInput {
  String? get projectId =>
      throw _privateConstructorUsedError; // ID проекта для фильтрации складов
  bool? get includeContractors =>
      throw _privateConstructorUsedError; // Включать склады контрагентов (по умолчанию true)
  WarehouseType? get type => throw _privateConstructorUsedError;

  /// Serializes this WarehouseSearchInput to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of WarehouseSearchInput
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $WarehouseSearchInputCopyWith<WarehouseSearchInput> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WarehouseSearchInputCopyWith<$Res> {
  factory $WarehouseSearchInputCopyWith(WarehouseSearchInput value,
          $Res Function(WarehouseSearchInput) then) =
      _$WarehouseSearchInputCopyWithImpl<$Res, WarehouseSearchInput>;
  @useResult
  $Res call({String? projectId, bool? includeContractors, WarehouseType? type});
}

/// @nodoc
class _$WarehouseSearchInputCopyWithImpl<$Res,
        $Val extends WarehouseSearchInput>
    implements $WarehouseSearchInputCopyWith<$Res> {
  _$WarehouseSearchInputCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of WarehouseSearchInput
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? projectId = freezed,
    Object? includeContractors = freezed,
    Object? type = freezed,
  }) {
    return _then(_value.copyWith(
      projectId: freezed == projectId
          ? _value.projectId
          : projectId // ignore: cast_nullable_to_non_nullable
              as String?,
      includeContractors: freezed == includeContractors
          ? _value.includeContractors
          : includeContractors // ignore: cast_nullable_to_non_nullable
              as bool?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as WarehouseType?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$WarehouseSearchInputImplCopyWith<$Res>
    implements $WarehouseSearchInputCopyWith<$Res> {
  factory _$$WarehouseSearchInputImplCopyWith(_$WarehouseSearchInputImpl value,
          $Res Function(_$WarehouseSearchInputImpl) then) =
      __$$WarehouseSearchInputImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? projectId, bool? includeContractors, WarehouseType? type});
}

/// @nodoc
class __$$WarehouseSearchInputImplCopyWithImpl<$Res>
    extends _$WarehouseSearchInputCopyWithImpl<$Res, _$WarehouseSearchInputImpl>
    implements _$$WarehouseSearchInputImplCopyWith<$Res> {
  __$$WarehouseSearchInputImplCopyWithImpl(_$WarehouseSearchInputImpl _value,
      $Res Function(_$WarehouseSearchInputImpl) _then)
      : super(_value, _then);

  /// Create a copy of WarehouseSearchInput
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? projectId = freezed,
    Object? includeContractors = freezed,
    Object? type = freezed,
  }) {
    return _then(_$WarehouseSearchInputImpl(
      projectId: freezed == projectId
          ? _value.projectId
          : projectId // ignore: cast_nullable_to_non_nullable
              as String?,
      includeContractors: freezed == includeContractors
          ? _value.includeContractors
          : includeContractors // ignore: cast_nullable_to_non_nullable
              as bool?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as WarehouseType?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$WarehouseSearchInputImpl implements _WarehouseSearchInput {
  _$WarehouseSearchInputImpl(
      {this.projectId, this.includeContractors, this.type});

  factory _$WarehouseSearchInputImpl.fromJson(Map<String, dynamic> json) =>
      _$$WarehouseSearchInputImplFromJson(json);

  @override
  final String? projectId;
// ID проекта для фильтрации складов
  @override
  final bool? includeContractors;
// Включать склады контрагентов (по умолчанию true)
  @override
  final WarehouseType? type;

  @override
  String toString() {
    return 'WarehouseSearchInput(projectId: $projectId, includeContractors: $includeContractors, type: $type)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WarehouseSearchInputImpl &&
            (identical(other.projectId, projectId) ||
                other.projectId == projectId) &&
            (identical(other.includeContractors, includeContractors) ||
                other.includeContractors == includeContractors) &&
            (identical(other.type, type) || other.type == type));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, projectId, includeContractors, type);

  /// Create a copy of WarehouseSearchInput
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$WarehouseSearchInputImplCopyWith<_$WarehouseSearchInputImpl>
      get copyWith =>
          __$$WarehouseSearchInputImplCopyWithImpl<_$WarehouseSearchInputImpl>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$WarehouseSearchInputImplToJson(
      this,
    );
  }
}

abstract class _WarehouseSearchInput implements WarehouseSearchInput {
  factory _WarehouseSearchInput(
      {final String? projectId,
      final bool? includeContractors,
      final WarehouseType? type}) = _$WarehouseSearchInputImpl;

  factory _WarehouseSearchInput.fromJson(Map<String, dynamic> json) =
      _$WarehouseSearchInputImpl.fromJson;

  @override
  String? get projectId; // ID проекта для фильтрации складов
  @override
  bool?
      get includeContractors; // Включать склады контрагентов (по умолчанию true)
  @override
  WarehouseType? get type;

  /// Create a copy of WarehouseSearchInput
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$WarehouseSearchInputImplCopyWith<_$WarehouseSearchInputImpl>
      get copyWith => throw _privateConstructorUsedError;
}

WarehouseSearchOutput _$WarehouseSearchOutputFromJson(
    Map<String, dynamic> json) {
  return _WarehouseSearchOutput.fromJson(json);
}

/// @nodoc
mixin _$WarehouseSearchOutput {
  List<Warehouse>? get warehouses =>
      throw _privateConstructorUsedError; // Список складов
  int? get total => throw _privateConstructorUsedError;

  /// Serializes this WarehouseSearchOutput to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of WarehouseSearchOutput
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $WarehouseSearchOutputCopyWith<WarehouseSearchOutput> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WarehouseSearchOutputCopyWith<$Res> {
  factory $WarehouseSearchOutputCopyWith(WarehouseSearchOutput value,
          $Res Function(WarehouseSearchOutput) then) =
      _$WarehouseSearchOutputCopyWithImpl<$Res, WarehouseSearchOutput>;
  @useResult
  $Res call({List<Warehouse>? warehouses, int? total});
}

/// @nodoc
class _$WarehouseSearchOutputCopyWithImpl<$Res,
        $Val extends WarehouseSearchOutput>
    implements $WarehouseSearchOutputCopyWith<$Res> {
  _$WarehouseSearchOutputCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of WarehouseSearchOutput
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? warehouses = freezed,
    Object? total = freezed,
  }) {
    return _then(_value.copyWith(
      warehouses: freezed == warehouses
          ? _value.warehouses
          : warehouses // ignore: cast_nullable_to_non_nullable
              as List<Warehouse>?,
      total: freezed == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$WarehouseSearchOutputImplCopyWith<$Res>
    implements $WarehouseSearchOutputCopyWith<$Res> {
  factory _$$WarehouseSearchOutputImplCopyWith(
          _$WarehouseSearchOutputImpl value,
          $Res Function(_$WarehouseSearchOutputImpl) then) =
      __$$WarehouseSearchOutputImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<Warehouse>? warehouses, int? total});
}

/// @nodoc
class __$$WarehouseSearchOutputImplCopyWithImpl<$Res>
    extends _$WarehouseSearchOutputCopyWithImpl<$Res,
        _$WarehouseSearchOutputImpl>
    implements _$$WarehouseSearchOutputImplCopyWith<$Res> {
  __$$WarehouseSearchOutputImplCopyWithImpl(_$WarehouseSearchOutputImpl _value,
      $Res Function(_$WarehouseSearchOutputImpl) _then)
      : super(_value, _then);

  /// Create a copy of WarehouseSearchOutput
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? warehouses = freezed,
    Object? total = freezed,
  }) {
    return _then(_$WarehouseSearchOutputImpl(
      warehouses: freezed == warehouses
          ? _value._warehouses
          : warehouses // ignore: cast_nullable_to_non_nullable
              as List<Warehouse>?,
      total: freezed == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$WarehouseSearchOutputImpl implements _WarehouseSearchOutput {
  _$WarehouseSearchOutputImpl({final List<Warehouse>? warehouses, this.total})
      : _warehouses = warehouses;

  factory _$WarehouseSearchOutputImpl.fromJson(Map<String, dynamic> json) =>
      _$$WarehouseSearchOutputImplFromJson(json);

  final List<Warehouse>? _warehouses;
  @override
  List<Warehouse>? get warehouses {
    final value = _warehouses;
    if (value == null) return null;
    if (_warehouses is EqualUnmodifiableListView) return _warehouses;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

// Список складов
  @override
  final int? total;

  @override
  String toString() {
    return 'WarehouseSearchOutput(warehouses: $warehouses, total: $total)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WarehouseSearchOutputImpl &&
            const DeepCollectionEquality()
                .equals(other._warehouses, _warehouses) &&
            (identical(other.total, total) || other.total == total));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_warehouses), total);

  /// Create a copy of WarehouseSearchOutput
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$WarehouseSearchOutputImplCopyWith<_$WarehouseSearchOutputImpl>
      get copyWith => __$$WarehouseSearchOutputImplCopyWithImpl<
          _$WarehouseSearchOutputImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$WarehouseSearchOutputImplToJson(
      this,
    );
  }
}

abstract class _WarehouseSearchOutput implements WarehouseSearchOutput {
  factory _WarehouseSearchOutput(
      {final List<Warehouse>? warehouses,
      final int? total}) = _$WarehouseSearchOutputImpl;

  factory _WarehouseSearchOutput.fromJson(Map<String, dynamic> json) =
      _$WarehouseSearchOutputImpl.fromJson;

  @override
  List<Warehouse>? get warehouses; // Список складов
  @override
  int? get total;

  /// Create a copy of WarehouseSearchOutput
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$WarehouseSearchOutputImplCopyWith<_$WarehouseSearchOutputImpl>
      get copyWith => throw _privateConstructorUsedError;
}
